<x-app-layout>
    <style>
        /* Star rating styles */
        .star-btn {
            border: none;
            background: none;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
        }
        .star-btn:hover {
            transform: scale(1.1);
        }
        .star-btn:focus {
            outline: none;
        }

        /* Modal styles */
        #reviewModal, #myReviewsModal {
            backdrop-filter: blur(4px);
        }

        /* Animation for loading spinner */
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* My Reviews Modal specific styles */
        #myReviewsModal {
            display: flex !important;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        #myReviewsModal.hidden {
            display: none !important;
        }

        #myReviewsModal .max-w-4xl {
            max-width: 56rem;
            max-height: 90vh;
            width: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        #myReviewsModal .modal-content {
            flex: 1;
            overflow-y: auto;
        }

        @media (max-width: 768px) {
            #myReviewsModal {
                padding: 0.5rem;
            }

            #myReviewsModal .max-w-4xl {
                max-width: 100%;
                max-height: 95vh;
            }
        }

        @media (max-width: 480px) {
            #myReviewsModal {
                padding: 0.25rem;
            }

            #myReviewsModal .max-w-4xl {
                max-height: 98vh;
            }
        }

        /* Review card hover effects */
        .review-card {
            transition: all 0.3s ease;
        }

        .review-card:hover {
            transform: translateY(-1px);
        }

        /* Custom scrollbar for reviews container */
        #myReviewsContainer::-webkit-scrollbar {
            width: 6px;
        }

        #myReviewsContainer::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        #myReviewsContainer::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        #myReviewsContainer::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Rating text styles */
        #ratingText {
            font-weight: 500;
            min-height: 20px;
        }

        /* Notification card hover effects */
        .notification-card {
            transition: all 0.3s ease;
        }
        .notification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    </style>

    <!-- Hero Section -->
    <x-hero-section
        title="Your Notifications"
        subtitle="Stay updated with the latest <span class='font-semibold text-gradient-primary'>news</span> and updates"
        description=""
        :showSteps="false"
    />

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <!-- Active Meetings Section -->
            @php
                $isMeetingVerificationEnabled = \App\Helpers\FeatureHelper::isMeetingVerificationEnabled();
                $activeMeetings = collect();

                if ($isMeetingVerificationEnabled) {
                    $activeMeetings = \App\Models\TimeSpendingBooking::with(['client', 'provider', 'meetingVerification'])
                        ->where(function($query) {
                            $query->where('client_id', Auth::user()->id)
                                  ->orWhere('provider_id', Auth::user()->id);
                        })
                        ->where('provider_status', 'accepted')
                        ->where('payment_status', 'paid')
                        ->where('booking_date', '>=', now()->subHours(1))
                        ->where('booking_date', '<=', now()->addHours(24))
                        ->whereRaw('DATE_ADD(booking_date, INTERVAL duration_hours HOUR) > NOW()')
                        ->whereNotIn('status', [
                            'auto_completed',
                            'auto_ended_partial',
                            'auto_ended_no_photos',
                            'manually_ended',
                            'cancelled',
                            'disputed'
                        ])
                        ->orderBy('booking_date', 'asc')
                        ->get()
                        ->filter(function($booking) {
                            // Only show meetings that haven't ended yet
                            // Once a meeting ends (by time or completion), it should move to notifications
                            if (!$booking->isPastMeetingTime()) {
                                return true; // Meeting hasn't ended yet
                            }

                            // If meeting time has passed, check if it's been completed with photos
                            // If completed, it should be in notifications, not active meetings
                            if ($booking->isMeetingCompleted()) {
                                return false; // Completed meetings go to notifications
                            }

                            // If time has passed but no completion, still exclude from active meetings
                            // These will be handled by auto-end process and moved to notifications
                            return false;
                        });
                }
            @endphp

            @if($isMeetingVerificationEnabled && $activeMeetings->count() > 0)
                <div class="mb-8">
                    <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200">
                        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <svg class="w-6 h-6 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Active Meetings
                        </h2>

                        <div class="space-y-4">
                            @foreach($activeMeetings as $meeting)
                                @php
                                    $isClient = Auth::user()->id === $meeting->client_id;
                                    $otherUser = $isClient ? $meeting->provider : $meeting->client;
                                    $verification = $meeting->meetingVerification;
                                    $currentTime = now();
                                    $meetingStart = $meeting->booking_date;
                                    $actualDuration = $meeting->actual_duration_hours ?? $meeting->duration_hours;
                                    $meetingEnd = $meeting->booking_date->copy()->addHours((float) $actualDuration);
                                    $isMeetingTime = $currentTime->between($meetingStart->copy()->subMinutes(15), $meetingEnd->copy()->addMinutes(15));
                                    $hasStarted = $verification && $verification->hasBothStartPhotos();
                                    $hasEnded = $verification && $verification->hasBothEndPhotos();

                                    // New logic for automatic meeting end and post-meeting actions
                                    $shouldAutoEnd = $meeting->shouldBeAutoEnded();
                                    $photoStatus = $meeting->getPhotoVerificationStatus();
                                    $hasAllPhotos = $photoStatus['has_all_photos'];
                                    $hasAnyPhotos = $photoStatus['has_any_photos'];
                                    $isPastEndTime = $meeting->isPastMeetingTime();

                                    // Check if current user has uploaded their start photo
                                    $userStartPhotoField = $isClient ? 'client_start_photo' : 'provider_start_photo';
                                    $userHasStartPhoto = $verification && !empty($verification->$userStartPhotoField);
                                    $userStartPhotoPath = $userHasStartPhoto ? $verification->$userStartPhotoField : null;
                                @endphp

                                <div class="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center mb-2">
                                                <img src="{{ $otherUser->profile_picture ? asset('storage/' . $otherUser->profile_picture) : asset('images/default-avatar.png') }}"
                                                     alt="{{ $otherUser->name }}"
                                                     class="w-10 h-10 rounded-full mr-3">
                                                <div class="flex-1">
                                                    <h3 class="font-semibold text-gray-800">{{ $otherUser->name }}</h3>
                                                    <p class="text-sm text-gray-600">
                                                        {{ $meetingStart->format('M d, Y') }} at {{ $meetingStart->format('h:i A') }} - {{ $meetingEnd->format('h:i A') }}
                                                    </p>
                                                </div>

                                                <!-- Small Meeting Photos in Header -->
                                                @php
                                                    $userEndPhotoField = $isClient ? 'client_end_photo' : 'provider_end_photo';
                                                    $otherStartPhotoField = $isClient ? 'provider_start_photo' : 'client_start_photo';
                                                    $otherEndPhotoField = $isClient ? 'provider_end_photo' : 'client_end_photo';

                                                    $userHasEndPhoto = $verification && !empty($verification->$userEndPhotoField);
                                                    $otherHasStartPhoto = $verification && !empty($verification->$otherStartPhotoField);
                                                    $otherHasEndPhoto = $verification && !empty($verification->$otherEndPhotoField);
                                                @endphp

                                                <!-- Meeting Verification Photos - Single Horizontal Row -->
                                                <div class="flex space-x-1 ml-2 flex-wrap">
                                                    @if($userHasStartPhoto)
                                                        <img src="{{ url('storage/' . $verification->$userStartPhotoField) }}"
                                                             alt="Your Start"
                                                             class="w-12 h-12 object-cover rounded border border-gray-300 cursor-pointer hover:opacity-80 transition-opacity"
                                                             onclick="openImageModal(this.src, 'Your Start Meeting Photo')"
                                                             title="Your Start Photo">
                                                    @endif

                                                    @if($userHasEndPhoto)
                                                        <img src="{{ url('storage/' . $verification->$userEndPhotoField) }}"
                                                             alt="Your End"
                                                             class="w-12 h-12 object-cover rounded border border-gray-300 cursor-pointer hover:opacity-80 transition-opacity"
                                                             onclick="openImageModal(this.src, 'Your End Meeting Photo')"
                                                             title="Your End Photo">
                                                    @endif

                                                    @if($otherHasStartPhoto)
                                                        <img src="{{ url('storage/' . $verification->$otherStartPhotoField) }}"
                                                             alt="{{ $otherUser->name }}'s Start"
                                                             class="w-12 h-12 object-cover rounded border border-gray-300 cursor-pointer hover:opacity-80 transition-opacity"
                                                             onclick="openImageModal(this.src, '{{ $otherUser->name }} Start Meeting Photo')"
                                                             title="{{ $otherUser->name }}'s Start Photo">
                                                    @endif

                                                    @if($otherHasEndPhoto)
                                                        <img src="{{ url('storage/' . $verification->$otherEndPhotoField) }}"
                                                             alt="{{ $otherUser->name }}'s End"
                                                             class="w-12 h-12 object-cover rounded border border-gray-300 cursor-pointer hover:opacity-80 transition-opacity"
                                                             onclick="openImageModal(this.src, '{{ $otherUser->name }} End Meeting Photo')"
                                                             title="{{ $otherUser->name }}'s End Photo">
                                                    @endif
                                                </div>
                                            </div>

                                            <div class="flex items-center space-x-4 text-sm">
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    @php
                                                        $actualDuration = $meeting->actual_duration_hours ?? $meeting->duration_hours;
                                                        $hours = floor($actualDuration);
                                                        $minutes = round(($actualDuration - $hours) * 60);
                                                    @endphp
                                                    @if($hours == 0)
                                                        {{ $minutes }} minutes
                                                    @elseif($minutes == 0)
                                                        {{ $hours }} hour{{ $hours > 1 ? 's' : '' }}
                                                    @else
                                                        {{ $hours }} hour{{ $hours > 1 ? 's' : '' }} {{ $minutes }} min
                                                    @endif
                                                </span>
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    </svg>
                                                    {{ Str::limit($meeting->meeting_location, 30) }}
                                                </span>
                                            </div>

                                            <!-- Legacy single photo display for JavaScript updates -->
                                            <div id="startPhotoDisplay-{{ $meeting->id }}" class="hidden">
                                                <img id="startPhotoImage-{{ $meeting->id }}" src="" alt="Start Meeting Photo" class="w-full h-8 object-cover rounded border border-gray-200">
                                            </div>
                                        </div>

                                        <div class="flex flex-col space-y-2">
                                            @php
                                                // Check if user can review this booking (hasn't reviewed yet)
                                                $canReview = $isPastEndTime && $hasAllPhotos && \App\Models\RatingReview::canReviewBooking($meeting->id, Auth::user()->id);
                                                $hasReviewed = $isPastEndTime && $hasAllPhotos && \App\Models\RatingReview::hasUserReviewed($meeting->id, Auth::user()->id);
                                            @endphp

                                            @if($canReview)
                                                <!-- Meeting ended with all photos and user hasn't reviewed yet - show Rate Experience button -->
                                                <button onclick="rateExperienceModal({{ $meeting->id }})"
                                                        class="px-4 py-2 text-xs font-medium text-white bg-yellow-600 hover:bg-yellow-700 rounded-md transition-colors"
                                                        style="background-color: #d97706; color: white; padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                        onmouseover="this.style.backgroundColor='#b45309'"
                                                        onmouseout="this.style.backgroundColor='#d97706'">
                                                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                                    </svg>
                                                    Rate & Review
                                                </button>
                                            @elseif($hasReviewed)
                                                <!-- Meeting ended with all photos but user has already reviewed - show completed message -->
                                                <div class="px-4 py-2 text-xs font-medium text-green-700 bg-green-100 rounded-md border border-green-200">
                                                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                    </svg>
                                                    Review Submitted
                                                </div>
                                            @elseif($isPastEndTime && !$hasAnyPhotos)
                                                <!-- Meeting ended with no photos - show {User} Don't Come button -->
                                                <button onclick="reportNoShow({{ $meeting->id }})"
                                                        class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                    </svg>
                                                    {{ $otherUser->name }} Didn't Come
                                                </button>
                                            @elseif($isPastEndTime && $hasAnyPhotos && !$hasAllPhotos)
                                                <!-- Meeting ended with some photos missing - show End Meeting button -->
                                                <button onclick="endMeetingManually({{ $meeting->id }})"
                                                        class="px-4 py-2 text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 rounded-lg transition-colors flex items-center">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                                    </svg>
                                                    End Meeting
                                                </button>
                                            @elseif(!$hasEnded && !$isPastEndTime)
                                                @if($userHasStartPhoto)
                                                    <!-- User has uploaded start photo, show End Meeting button -->
                                                    <button id="startMeetingBtn-{{ $meeting->id }}"
                                                            onclick="endMeeting({{ $meeting->id }})"
                                                            class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center">
                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                                        </svg>
                                                        <span class="button-text">End Meeting</span>
                                                    </button>
                                                @elseif($isMeetingTime)
                                                    <!-- Meeting time and no start photo yet, show Start Meeting button -->
                                                    <button id="startMeetingBtn-{{ $meeting->id }}"
                                                            data-booking-id="{{ $meeting->id }}"
                                                            onclick="handleStartMeetingClick({{ $meeting->id }})"
                                                            class="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors flex items-center">
                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        <span class="button-text">Start Meeting</span>
                                                    </button>
                                                @endif
                                            @endif

                                            <!-- Status indicators -->
                                            @if($hasEnded)
                                                <div class="px-4 py-2 text-sm font-medium text-green-800 bg-green-100 rounded-lg flex items-center">
                                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    Completed
                                                </div>
                                            @elseif($hasStarted && !$isPastEndTime)
                                                <div class="px-4 py-2 text-sm font-medium text-blue-800 bg-blue-100 rounded-lg flex items-center">
                                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9.5 9.293 10.793a1 1 0 101.414 1.414l2-2a1 1 0 000-1.414z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    In Progress
                                                </div>
                                            @elseif($isPastEndTime)
                                                <div class="px-4 py-2 text-sm font-medium text-gray-800 bg-gray-100 rounded-lg flex items-center">
                                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    Time Ended
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif



            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <!-- Pending Bookings Section -->
                    @php
                        $pendingBookings = \App\Models\TimeSpendingBooking::with(['provider'])
                            ->where('client_id', Auth::user()->id)
                            ->where('provider_status', 'pending')
                            ->where('payment_status', 'paid')
                            ->where('status', '!=', 'cancelled')
                            ->where('booking_date', '>', now()) // Only show bookings that haven't started yet
                            ->orderBy('booking_date', 'asc')
                            ->get();
                    @endphp

                    @if ($pendingBookings->count() > 0)
                        <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="flex items-center mb-3">
                                <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <h3 class="text-lg font-semibold text-yellow-800">Your Pending Bookings</h3>
                                <span class="ml-2 px-2 py-1 text-xs font-medium bg-yellow-200 text-yellow-800 rounded-full">
                                    {{ $pendingBookings->count() }}
                                </span>
                            </div>
                            <p class="text-sm text-yellow-700 mb-4">These bookings are waiting for provider acceptance. You can cancel them if needed.</p>

                            <div class="space-y-3">
                                @foreach ($pendingBookings as $pendingBooking)
                                    <div class="bg-white rounded-lg p-4 border border-yellow-200">
                                        <div class="flex justify-between items-start">
                                            <div class="flex-1">
                                                <div class="flex items-center mb-2">
                                                    <h4 class="font-medium text-gray-900">
                                                        Booking with
                                                        <a href="/find-person/{{ $pendingBooking->provider->id }}" class="text-blue-600 hover:text-blue-800 hover:underline">
                                                            {{ $pendingBooking->provider->name }}
                                                        </a>
                                                    </h4>
                                                    <span class="ml-2 px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                                                        Pending
                                                    </span>
                                                </div>

                                                <div class="grid grid-cols-2 gap-3 text-sm text-gray-600 mb-3">
                                                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                        </svg>
                                                        <span>{{ $pendingBooking->booking_date->format('M d, Y') }}</span>
                                                    </div>
                                                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        @php
                                                            $actualDuration = $pendingBooking->actual_duration_hours ?? $pendingBooking->duration_hours;
                                                            $hours = floor($actualDuration);
                                                            $minutes = round(($actualDuration - $hours) * 60);
                                                            $durationText = $hours == 0 ? $minutes . 'm' : ($minutes == 0 ? $hours . 'h' : $hours . 'h ' . $minutes . 'm');
                                                        @endphp
                                                        <span>{{ $pendingBooking->booking_date->format('h:i A') }} ({{ $durationText }})</span>
                                                    </div>
                                                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                        </svg>
                                                        <span class="font-medium">₹{{ number_format($pendingBooking->total_amount) }}</span>
                                                    </div>
                                                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        </svg>
                                                        <span class="truncate">{{ Str::limit($pendingBooking->meeting_location, 30) }}</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="ml-4 flex space-x-2">
                                                @if ($pendingBooking->provider_status === 'pending' && $pendingBooking->status !== 'cancelled')
                                                    <button onclick="showCancelModal({{ $pendingBooking->id }})"
                                                            class="px-3 py-1 text-xs font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors">
                                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                        </svg>
                                                        Cancel Booking
                                                    </button>

                                                    <button onclick="redirectToUpdateBooking({{ $pendingBooking->id }})"
                                                            class="px-3 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
                                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                        </svg>
                                                        Update Booking
                                                    </button>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Your Notifications</h3>

                        @if ($notifications->count() > 0)
                            <form action="{{ route('notifications.mark-all-as-read') }}" method="POST">
                                @csrf
                                <button type="submit" class="px-3 py-1 text-xs font-medium text-indigo-600 hover:text-indigo-900 bg-indigo-50 hover:bg-indigo-100 rounded-md transition-colors">
                                    Mark All Read
                                </button>
                            </form>
                        @endif
                    </div>

                    @if ($notifications->count() > 0)
                        <div class="space-y-2">
                            @foreach ($notifications as $notification)
                                <div class="border rounded-lg p-4 {{ $notification->is_read ? 'bg-white border-gray-200' : 'bg-blue-50 border-blue-200' }}">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-center gap-2 mb-1">
                                                @php
                                                    // Get booking data for title generation
                                                    $titleBooking = null;
                                                    if (in_array($notification->type, ['booking_request', 'booking_accepted', 'booking_rejected', 'booking_auto_rejected', 'booking_cancelled', 'booking_updated']) && isset($notification->data['booking_id'])) {
                                                        $titleBooking = \App\Models\TimeSpendingBooking::with(['client', 'provider'])->find($notification->data['booking_id']);
                                                    }
                                                @endphp

                                                <h3 class="text-sm font-semibold text-gray-900 truncate">
                                                    @if ($notification->type === 'booking_request' && $titleBooking)
                                                        @if (isset($notification->data['is_update']) && $notification->data['is_update'])
                                                            Updated Booking Request from <a href="{{ route('find-person.show-from-hire-request', $titleBooking->client->id) }}" class="text-blue-600 hover:text-blue-800 hover:underline">{{ $titleBooking->client->name }}</a>
                                                        @else
                                                            New Booking Request from <a href="{{ route('find-person.show-from-hire-request', $titleBooking->client->id) }}" class="text-blue-600 hover:text-blue-800 hover:underline">{{ $titleBooking->client->name }}</a>
                                                        @endif
                                                    @elseif ($notification->type === 'booking_updated' && $titleBooking)
                                                        Updated Booking Request from <a href="{{ route('find-person.show-from-hire-request', $titleBooking->client->id) }}" class="text-blue-600 hover:text-blue-800 hover:underline">{{ $titleBooking->client->name }}</a>
                                                    @elseif ($notification->type === 'booking_cancelled' && $titleBooking)
                                                        @if (Auth::user()->id === $titleBooking->provider_id)
                                                            Booking Cancelled by <a href="{{ route('find-person.show-from-hire-request', $titleBooking->client->id) }}" class="text-blue-600 hover:text-blue-800 hover:underline">{{ $titleBooking->client->name }}</a>
                                                        @else
                                                            Booking Cancelled by <a href="/find-person/{{ $titleBooking->provider->id }}" class="text-blue-600 hover:text-blue-800 hover:underline">{{ $titleBooking->provider->name }}</a>
                                                        @endif
                                                    @else
                                                        {{ $notification->title }}
                                                    @endif
                                                </h3>
                                                <span class="px-2 py-1 text-xs font-medium rounded-full flex-shrink-0
                                                    @if ($notification->type === 'general') bg-indigo-100 text-indigo-700
                                                    @elseif ($notification->type === 'payment') bg-green-100 text-green-700
                                                    @elseif ($notification->type === 'refund_processed') bg-indigo-100 text-indigo-700
                                                    @elseif ($notification->type === 'match') bg-purple-100 text-purple-700
                                                    @elseif ($notification->type === 'couple_activity') bg-pink-100 text-pink-700
                                                    @elseif ($notification->type === 'review_reminder') bg-yellow-100 text-yellow-700
                                                    @elseif ($notification->type === 'review_received') bg-green-100 text-green-700
                                                    @elseif ($notification->type === 'booking_request')
                                                        @php
                                                            $colorBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id'] ?? null);
                                                        @endphp
                                                        @if ($colorBooking && $colorBooking->status === 'auto_cancelled')
                                                            bg-orange-100 text-orange-700
                                                        @elseif ($colorBooking && $colorBooking->provider_status === 'rejected' && $colorBooking->rejection_reason === 'Provider selected another client for this time slot')
                                                            bg-gray-100 text-gray-700
                                                        @else
                                                            bg-orange-100 text-orange-700
                                                        @endif
                                                    @elseif ($notification->type === 'booking_accepted') bg-green-100 text-green-700
                                                    @elseif ($notification->type === 'booking_rejected') bg-red-100 text-red-700
                                                    @elseif ($notification->type === 'booking_auto_rejected') bg-gray-100 text-gray-700
                                                    @elseif ($notification->type === 'booking_auto_cancelled') bg-orange-100 text-orange-700
                                                    @elseif ($notification->type === 'booking_opportunity_missed') bg-orange-100 text-orange-700
                                                    @elseif ($notification->type === 'payment_received') bg-emerald-100 text-emerald-700
                                                    @elseif ($notification->type === 'withdrawal_requested') bg-yellow-100 text-yellow-700
                                                    @elseif ($notification->type === 'withdrawal_completed') bg-green-100 text-green-700
                                                    @elseif ($notification->type === 'withdrawal_rejected') bg-red-100 text-red-700
                                                    @elseif ($notification->type === 'withdrawal_cancelled') bg-gray-100 text-gray-700
                                                    @elseif ($notification->type === 'sugar_partner_exchange_initiated') bg-pink-100 text-pink-700
                                                    @elseif ($notification->type === 'sugar_partner_payment_completed') bg-green-100 text-green-700
                                                    @elseif ($notification->type === 'sugar_partner_profiles_viewable') bg-purple-100 text-purple-700
                                                    @elseif ($notification->type === 'sugar_partner_response_received') bg-blue-100 text-blue-700
                                                    @elseif ($notification->type === 'sugar_partner_match_success') bg-green-100 text-green-700
                                                    @elseif ($notification->type === 'sugar_partner_mismatch') bg-red-100 text-red-700
                                                    @elseif ($notification->type === 'sugar_partner_response_pending') bg-yellow-100 text-yellow-700
                                                    @elseif ($notification->type === 'sugar_partner_hard_reject_converted') bg-orange-100 text-orange-700
                                                    @elseif ($notification->type === 'sugar_partner_rejection_cleared') bg-teal-100 text-teal-700
                                                    @elseif ($notification->type === 'meeting_completed_rate') bg-yellow-100 text-yellow-700
                                                    @elseif ($notification->type === 'meeting_completed_no_photos') bg-red-100 text-red-700
                                                    @elseif ($notification->type === 'meeting_completed_partial') bg-orange-100 text-orange-700
                                                    @else bg-gray-100 text-gray-700
                                                    @endif">
                                                    @if ($notification->type === 'booking_request')
                                                        @php
                                                            $badgeBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id'] ?? null);
                                                        @endphp
                                                        @if ($badgeBooking && $badgeBooking->status === 'auto_cancelled')
                                                            <span class="flex items-center">
                                                                Auto-Cancelled
                                                                <div class="relative inline-block ml-1 group">
                                                                    <span class="inline-flex items-center justify-center w-3 h-3 text-xs font-bold text-white bg-orange-600 rounded-full cursor-help">
                                                                        i
                                                                    </span>
                                                                    <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs text-white bg-gray-800 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                                                                        Booking was automatically cancelled due to no response before meeting time.
                                                                        <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                                                                    </div>
                                                                </div>
                                                            </span>
                                                        @elseif ($badgeBooking && $badgeBooking->provider_status === 'accepted')
                                                            Booking Accepted
                                                        @elseif ($badgeBooking && $badgeBooking->provider_status === 'rejected')
                                                            @if ($badgeBooking->rejection_reason === 'Provider selected another client for this time slot')
                                                                Booking Auto-Rejected
                                                            @else
                                                                Booking Rejected
                                                            @endif
                                                        @elseif (isset($notification->data['is_update']) && $notification->data['is_update'])
                                                            Updated Booking Request
                                                        @else
                                                            Booking Request
                                                        @endif
                                                    @elseif ($notification->type === 'booking_auto_rejected')
                                                        Booking Auto-Rejected
                                                    @elseif ($notification->type === 'booking_auto_cancelled')
                                                        <span class="flex items-center">
                                                            Booking Auto-Cancelled
                                                            <div class="relative inline-block ml-1 group">
                                                                <span class="inline-flex items-center justify-center w-3 h-3 text-xs font-bold text-white bg-orange-600 rounded-full cursor-help">
                                                                    i
                                                                </span>
                                                                <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs text-white bg-gray-800 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                                                                    Booking was automatically cancelled due to no response before meeting time.
                                                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                                                                </div>
                                                            </div>
                                                        </span>
                                                    @elseif ($notification->type === 'booking_opportunity_missed')
                                                        Missed Opportunity
                                                    @elseif ($notification->type === 'refund_processed')
                                                        Refund to your wallet
                                                    @elseif ($notification->type === 'meeting_start_reminder')
                                                        Meeting Start - Photo Required (Legacy)
                                                    @elseif ($notification->type === 'meeting_end_reminder')
                                                        Meeting End - Photo Required
                                                    @elseif ($notification->type === 'meeting_completed')
                                                        Meeting Completed
                                                    @elseif ($notification->type === 'review_reminder')
                                                        Review Reminder
                                                    @elseif ($notification->type === 'review_received')
                                                        Review Received
                                                    @elseif ($notification->type === 'withdrawal_requested')
                                                        Withdrawal Request Submitted
                                                    @elseif ($notification->type === 'withdrawal_completed')
                                                        Withdrawal Completed
                                                    @elseif ($notification->type === 'withdrawal_rejected')
                                                        Withdrawal Request Rejected
                                                    @elseif ($notification->type === 'withdrawal_cancelled')
                                                        Withdrawal Cancelled
                                                    @elseif ($notification->type === 'sugar_partner_exchange_initiated')
                                                        Sugar Partner Exchange
                                                    @elseif ($notification->type === 'sugar_partner_payment_completed')
                                                        Payment Successful
                                                    @elseif ($notification->type === 'sugar_partner_profiles_viewable')
                                                        Profiles Ready
                                                    @elseif ($notification->type === 'sugar_partner_response_received')
                                                        Response Received
                                                    @elseif ($notification->type === 'sugar_partner_match_success')
                                                        Match Success!
                                                    @elseif ($notification->type === 'sugar_partner_mismatch')
                                                        Response Mismatch
                                                    @elseif ($notification->type === 'sugar_partner_response_pending')
                                                        Response Needed
                                                    @elseif ($notification->type === 'sugar_partner_hard_reject_converted')
                                                        Hard Reject Converted
                                                    @elseif ($notification->type === 'sugar_partner_rejection_cleared')
                                                        Rejection Cleared
                                                    @elseif ($notification->type === 'meeting_completed_rate')
                                                        Rate Experience
                                                    @elseif ($notification->type === 'meeting_completed_no_photos')
                                                        Start Photo Required
                                                    @elseif ($notification->type === 'meeting_completed_partial')
                                                        Resolve Required
                                                    @else
                                                        {{ ucfirst(str_replace('_', ' ', $notification->type)) }}
                                                    @endif
                                                </span>
                                            </div>
                                            @if (in_array($notification->type, ['booking_request', 'booking_accepted', 'booking_rejected', 'booking_auto_rejected', 'booking_cancelled', 'booking_updated', 'payment_received', 'meeting_end_reminder', 'meeting_completed', 'meeting_completed_rate', 'meeting_completed_no_photos', 'meeting_completed_partial', 'review_reminder', 'review_received']) && isset($notification->data['booking_id']))
                                                @php
                                                    $booking = \App\Models\TimeSpendingBooking::with(['client', 'provider'])->find($notification->data['booking_id']);
                                                @endphp

                                                <!-- Short message with expand button -->
                                                <div class="flex items-start gap-2">
                                                    <p class="text-sm text-gray-700 mb-2 flex-1">
                                                        {{ Str::limit($notification->message ?? $notification->body, 80) }}
                                                    </p>
                                                    <button onclick="toggleDetails('notification-{{ $notification->id }}')"
                                                            class="text-blue-600 hover:text-blue-800 flex-shrink-0 mt-1">
                                                        <svg id="arrow-notification-{{ $notification->id }}" class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                        </svg>
                                                    </button>
                                                </div>

                                                <!-- Full details (hidden by default) -->
                                                <div id="details-notification-{{ $notification->id }}" class="hidden mt-2">
                                                    @if ($booking)
                                                        <!-- Enhanced Booking Details -->
                                                        <div class="bg-gray-50 rounded-lg p-3">
                                                            <div class="flex items-start justify-between mb-2">
                                                                <div class="flex-1">
                                                                    @if ($notification->type === 'booking_request')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            <a href="{{ route('find-person.show-from-hire-request', $booking->client->id) }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                {{ $booking->client->name }}
                                                                            </a>
                                                                            @if (isset($notification->data['is_update']) && $notification->data['is_update'])
                                                                                @php
                                                                                    $actualDuration = $booking->actual_duration_hours ?? $booking->duration_hours;
                                                                                    $hours = floor($actualDuration);
                                                                                    $minutes = round(($actualDuration - $hours) * 60);
                                                                                    $durationText = $hours == 0 ? $minutes . ' minutes' : ($minutes == 0 ? $hours . ' hour' . ($hours > 1 ? 's' : '') : $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ' . $minutes . ' min');
                                                                                @endphp
                                                                                has updated their booking request for {{ $durationText }}
                                                                            @else
                                                                                @php
                                                                                    $actualDuration = $booking->actual_duration_hours ?? $booking->duration_hours;
                                                                                    $hours = floor($actualDuration);
                                                                                    $minutes = round(($actualDuration - $hours) * 60);
                                                                                    $durationText = $hours == 0 ? $minutes . ' minutes' : ($minutes == 0 ? $hours . ' hour' . ($hours > 1 ? 's' : '') : $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ' . $minutes . ' min');
                                                                                @endphp
                                                                                wants to hire you for {{ $durationText }}
                                                                            @endif
                                                                        </p>
                                                                    @elseif ($notification->type === 'booking_accepted')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            <a href="/find-person/{{ $booking->provider->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                {{ $booking->provider->name }}
                                                                            </a>
                                                                            has accepted your booking request
                                                                        </p>
                                                                    @elseif ($notification->type === 'booking_rejected')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            <a href="/find-person/{{ $booking->provider->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                {{ $booking->provider->name }}
                                                                            </a>
                                                                            has rejected your booking request
                                                                        </p>
                                                                        @if(isset($notification->data['reason']) && $notification->data['reason'])
                                                                            <div class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                                                                                <p class="text-sm text-red-800">
                                                                                    <span class="font-medium">Reason:</span> {{ $notification->data['reason'] }}
                                                                                </p>
                                                                            </div>
                                                                        @endif
                                                                    @elseif ($notification->type === 'payment_received')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            Payment received from
                                                                            <a href="/find-person/{{ $booking->client->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                {{ $booking->client->name }}
                                                                            </a>
                                                                        </p>
                                                                    @elseif ($notification->type === 'booking_cancelled')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            @if (Auth::user()->id === $booking->provider_id)
                                                                                <a href="/find-person/{{ $booking->client->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                    {{ $booking->client->name }}
                                                                                </a>
                                                                                has cancelled their booking request
                                                                            @else
                                                                                Your booking with
                                                                                <a href="/find-person/{{ $booking->provider->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                    {{ $booking->provider->name }}
                                                                                </a>
                                                                                has been cancelled
                                                                            @endif
                                                                        </p>
                                                                    @elseif ($notification->type === 'booking_auto_rejected')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            Your booking request with
                                                                            <a href="/find-person/{{ $booking->provider->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                {{ $booking->provider->name }}
                                                                            </a>
                                                                            was automatically rejected because they accepted another request for the same time slot
                                                                        </p>
                                                                    @elseif ($notification->type === 'booking_updated')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            @if (Auth::user()->id === $booking->provider_id)
                                                                                <a href="/find-person/{{ $booking->client->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                    {{ $booking->client->name }}
                                                                                </a>
                                                                                has updated their booking request
                                                                            @else
                                                                                You have updated your booking with
                                                                                <a href="/find-person/{{ $booking->provider->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                    {{ $booking->provider->name }}
                                                                                </a>
                                                                            @endif
                                                                        </p>
                                                                    @elseif (in_array($notification->type, ['meeting_completed_rate', 'meeting_completed_no_photos', 'meeting_completed_partial']))
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            @if (isset($notification->data['other_user_name']))
                                                                                {{ $notification->message }}
                                                                            @else
                                                                                {{ $notification->message }}
                                                                            @endif
                                                                        </p>
                                                                    @endif

                                                                    <!-- Booking Details Grid -->
                                                                    <div class="grid grid-cols-2 gap-3 text-xs">
                                                                        <div class="flex items-center">
                                                                            <svg class="w-3 h-3 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                                            </svg>
                                                                            <span class="text-gray-600">{{ $booking->booking_date->format('M d, Y') }}</span>
                                                                        </div>
                                                                        <div class="flex items-center">
                                                                            <svg class="w-3 h-3 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                            </svg>
                                                                            <span class="text-gray-600">
                                                                                {{ $booking->booking_date->format('h:i A') }} -
                                                                                @php $actualDuration = $booking->actual_duration_hours ?? $booking->duration_hours; @endphp
                                                                                {{ $booking->booking_date->copy()->addHours((float) $actualDuration)->format('h:i A') }}
                                                                            </span>
                                                                        </div>
                                                                        <div class="flex items-center">
                                                                            <svg class="w-3 h-3 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                            </svg>
                                                                            @php
                                                                                $actualDuration = $booking->actual_duration_hours ?? $booking->duration_hours;
                                                                                $hours = floor($actualDuration);
                                                                                $minutes = round(($actualDuration - $hours) * 60);
                                                                                $durationText = $hours == 0 ? $minutes . ' minutes' : ($minutes == 0 ? $hours . ' hour' . ($hours > 1 ? 's' : '') : $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ' . $minutes . ' min');
                                                                            @endphp
                                                                            <span class="text-gray-600">{{ $durationText }}</span>
                                                                        </div>
                                                                        <div class="flex items-center">
                                                                            <svg class="w-3 h-3 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                                            </svg>
                                                                            <span class="text-gray-600">
                                                                                @if ($notification->type === 'payment_received')
                                                                                    ₹{{ number_format($notification->data['amount'] ?? ($booking->base_amount ?? $booking->total_amount), 0) }}
                                                                                @else
                                                                                    ₹{{ number_format($booking->base_amount ?? $booking->total_amount, 0) }}
                                                                                @endif
                                                                            </span>
                                                                        </div>
                                                                        @if ($booking->meeting_location)
                                                                            <div class="flex items-center col-span-2">
                                                                                <svg class="w-3 h-3 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                                </svg>
                                                                                <span class="text-gray-600">{{ Str::limit($booking->meeting_location, 40) }}</span>
                                                                            </div>
                                                                        @endif
                                                                        @if ($booking->notes)
                                                                            <div class="col-span-2 mt-2 p-2 bg-blue-50 rounded border-l-2 border-blue-200">
                                                                                <div class="flex items-start">
                                                                                    <svg class="w-3 h-3 text-blue-500 mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                                                                                    </svg>
                                                                                    <div>
                                                                                        <p class="text-xs font-medium text-blue-700 mb-1">Note:</p>
                                                                                        <p class="text-xs text-blue-600">{{ $booking->notes }}</p>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Meeting Verification Section -->
                                                        @if ($isMeetingVerificationEnabled && in_array($notification->type, ['booking_accepted', 'meeting_end_reminder', 'meeting_completed', 'meeting_completed_rate', 'meeting_completed_no_photos', 'meeting_completed_partial']) && $booking && $booking->provider_status === 'accepted')
                                                            @php
                                                                $verification = $booking->meetingVerification;
                                                                $currentTime = now();
                                                                $bookingStartTime = $booking->booking_date;
                                                                $actualDuration = $booking->actual_duration_hours ?? $booking->duration_hours;
                                                                $bookingEndTime = $booking->booking_date->copy()->addHours((float) $actualDuration);
                                                                $isWithinMeetingTime = $currentTime->between($bookingStartTime->copy()->subMinutes(15), $bookingEndTime->copy()->addMinutes(15));
                                                                $isMeetingTime = $currentTime->between($bookingStartTime, $bookingEndTime);
                                                                $isMeetingStarted = $verification && $verification->hasBothStartPhotos();
                                                                $isMeetingEnded = $verification && $verification->hasBothEndPhotos();
                                                                $userIsClient = Auth::user()->id === $booking->client_id;
                                                            @endphp

                                                            @if ($isWithinMeetingTime || $verification)
                                                                <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                                                    <h4 class="text-sm font-semibold text-blue-800 mb-3 flex items-center">
                                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                        </svg>
                                                                        Meeting Verification
                                                                    </h4>

                                                                    @if (!$verification)
                                                                        <p class="text-sm text-blue-700 mb-3">
                                                                            When your meeting starts, both you and {{ $userIsClient ? $booking->provider->name : $booking->client->name }} will need to take photos together using your device camera to verify the meeting. Each photo should capture both participants in the same image.
                                                                        </p>
                                                                    @else
                                                                        @php
                                                                            $verificationStatus = $verification->getUserVerificationStatus(Auth::user()->id);
                                                                        @endphp

                                                                        @php
                                                                            // Get photo field names based on user role
                                                                            $userStartPhotoField = $userIsClient ? 'client_start_photo' : 'provider_start_photo';
                                                                            $userEndPhotoField = $userIsClient ? 'client_end_photo' : 'provider_end_photo';
                                                                            $partnerStartPhotoField = $userIsClient ? 'provider_start_photo' : 'client_start_photo';
                                                                            $partnerEndPhotoField = $userIsClient ? 'provider_end_photo' : 'client_end_photo';
                                                                            $partnerName = $userIsClient ? $booking->provider->name : $booking->client->name;

                                                                            // Check which photos exist
                                                                            $userStartPhoto = $verification && !empty($verification->$userStartPhotoField) ? $verification->$userStartPhotoField : null;
                                                                            $userEndPhoto = $verification && !empty($verification->$userEndPhotoField) ? $verification->$userEndPhotoField : null;
                                                                            $partnerStartPhoto = $verification && !empty($verification->$partnerStartPhotoField) ? $verification->$partnerStartPhotoField : null;
                                                                            $partnerEndPhoto = $verification && !empty($verification->$partnerEndPhotoField) ? $verification->$partnerEndPhotoField : null;
                                                                        @endphp

                                                                        <!-- Meeting Verification Photos - Single Horizontal Row -->
                                                                        @if ($userStartPhoto || $userEndPhoto || $partnerStartPhoto || $partnerEndPhoto)
                                                                            <div class="mb-4">
                                                                                <h5 class="text-sm font-medium text-gray-900 mb-2">Meeting Photos</h5>
                                                                                <div class="flex space-x-2 overflow-x-auto">
                                                                                    @if ($userStartPhoto)
                                                                                        <div class="flex-shrink-0">
                                                                                            <img src="{{ url('storage/' . $userStartPhoto) }}"
                                                                                                 alt="Your Start Photo"
                                                                                                 class="w-20 h-20 object-cover rounded border border-gray-300 cursor-pointer hover:opacity-80 transition-opacity"
                                                                                                 onclick="openImageModal(this.src, 'Your Start Meeting Photo')"
                                                                                                 title="Your Start Photo">
                                                                                            <p class="text-xs text-center text-gray-500 mt-1">Your Start</p>
                                                                                        </div>
                                                                                    @endif

                                                                                    @if ($partnerStartPhoto)
                                                                                        <div class="flex-shrink-0">
                                                                                            <img src="{{ url('storage/' . $partnerStartPhoto) }}"
                                                                                                 alt="{{ $partnerName }}'s Start Photo"
                                                                                                 class="w-20 h-20 object-cover rounded border border-gray-300 cursor-pointer hover:opacity-80 transition-opacity"
                                                                                                 onclick="openImageModal(this.src, '{{ $partnerName }} Start Meeting Photo')"
                                                                                                 title="{{ $partnerName }}'s Start Photo">
                                                                                            <p class="text-xs text-center text-gray-500 mt-1">{{ Str::limit($partnerName, 10) }} Start</p>
                                                                                        </div>
                                                                                    @endif

                                                                                    @if ($userEndPhoto)
                                                                                        <div class="flex-shrink-0">
                                                                                            <img src="{{ url('storage/' . $userEndPhoto) }}"
                                                                                                 alt="Your End Photo"
                                                                                                 class="w-20 h-20 object-cover rounded border border-gray-300 cursor-pointer hover:opacity-80 transition-opacity"
                                                                                                 onclick="openImageModal(this.src, 'Your End Meeting Photo')"
                                                                                                 title="Your End Photo">
                                                                                            <p class="text-xs text-center text-gray-500 mt-1">Your End</p>
                                                                                        </div>
                                                                                    @endif

                                                                                    @if ($partnerEndPhoto)
                                                                                        <div class="flex-shrink-0">
                                                                                            <img src="{{ url('storage/' . $partnerEndPhoto) }}"
                                                                                                 alt="{{ $partnerName }}'s End Photo"
                                                                                                 class="w-20 h-20 object-cover rounded border border-gray-300 cursor-pointer hover:opacity-80 transition-opacity"
                                                                                                 onclick="openImageModal(this.src, '{{ $partnerName }} End Meeting Photo')"
                                                                                                 title="{{ $partnerName }}'s End Photo">
                                                                                            <p class="text-xs text-center text-gray-500 mt-1">{{ Str::limit($partnerName, 10) }} End</p>
                                                                                        </div>
                                                                                    @endif
                                                                                </div>
                                                                            </div>
                                                                        @endif

                                                                        <!-- Meeting Status Summary -->
                                                                        <div class="space-y-2">
                                                                            <div class="flex items-center justify-between p-2 bg-white rounded border">
                                                                                <div class="flex items-center">
                                                                                    <div class="w-5 h-5 rounded-full mr-2 flex items-center justify-center {{ $verificationStatus['start_photo_uploaded'] ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400' }}">
                                                                                        @if ($verificationStatus['start_photo_uploaded'])
                                                                                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                                                            </svg>
                                                                                        @else
                                                                                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"></path>
                                                                                            </svg>
                                                                                        @endif
                                                                                    </div>
                                                                                    <div>
                                                                                        <p class="text-xs font-medium text-gray-900">Start Photos</p>
                                                                                        <p class="text-xs text-gray-500">
                                                                                            {{ $verificationStatus['start_photo_uploaded'] ? 'Both uploaded' : 'Required when meeting starts' }}
                                                                                        </p>
                                                                                    </div>
                                                                                </div>
                                                                                @if ($isMeetingTime && !$verificationStatus['start_photo_uploaded'])
                                                                                    <button onclick="openCameraModal('start', {{ $booking->id }})"
                                                                                            class="px-2 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded transition-colors">
                                                                                        Take Photo
                                                                                    </button>
                                                                                @endif
                                                                            </div>

                                                                            @if ($verificationStatus['meeting_started'])
                                                                                <!-- Meeting End Status -->
                                                                                <div class="flex items-center justify-between p-2 bg-white rounded border">
                                                                                    <div class="flex items-center">
                                                                                        <div class="w-5 h-5 rounded-full mr-2 flex items-center justify-center {{ $verificationStatus['end_photo_uploaded'] ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400' }}">
                                                                                            @if ($verificationStatus['end_photo_uploaded'])
                                                                                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                                                                </svg>
                                                                                            @else
                                                                                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"></path>
                                                                                                </svg>
                                                                                            @endif
                                                                                        </div>
                                                                                        <div>
                                                                                            <p class="text-xs font-medium text-gray-900">End Photos</p>
                                                                                            <p class="text-xs text-gray-500">
                                                                                                {{ $verificationStatus['end_photo_uploaded'] ? 'Both uploaded' : 'Required when meeting ends' }}
                                                                                            </p>
                                                                                        </div>
                                                                                    </div>
                                                                                    @if (!$verificationStatus['end_photo_uploaded'])
                                                                                        <button onclick="openCameraModal('end', {{ $booking->id }})"
                                                                                                class="px-2 py-1 text-xs font-medium text-white bg-red-600 hover:bg-red-700 rounded transition-colors">
                                                                                            Take Photo
                                                                                        </button>
                                                                                    @endif
                                                                                </div>
                                                                            @endif

                                                                            @if ($verificationStatus['is_verified'])
                                                                                <div class="p-2 bg-green-50 border border-green-200 rounded">
                                                                                    <div class="flex items-center">
                                                                                        <svg class="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                                                        </svg>
                                                                                        <div>
                                                                                            <p class="text-xs font-semibold text-green-800">Meeting Verified!</p>
                                                                                            <p class="text-xs text-green-700">Duration: {{ $verificationStatus['duration'] }}</p>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            @endif
                                                                        </div>
                                                                    @endif
                                                                </div>
                                                            @endif
                                                        @endif
                                                    @endif
                                                </div>
                                            @elseif (in_array($notification->type, ['meeting_started_verified', 'meeting_ended_verified']))
                                                <!-- Meeting Images Display -->
                                                <div class="flex items-start gap-2">
                                                    <p class="text-sm text-gray-700 mb-2 flex-1">
                                                        {{ Str::limit($notification->message ?? $notification->body, 80) }}
                                                    </p>
                                                    <button onclick="toggleDetails('notification-{{ $notification->id }}')"
                                                            class="text-blue-600 hover:text-blue-800 flex-shrink-0 mt-1">
                                                        <svg id="arrow-notification-{{ $notification->id }}" class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                        </svg>
                                                    </button>
                                                </div>

                                                <!-- Meeting Images Details -->
                                                <div id="details-notification-{{ $notification->id }}" class="hidden mt-2">
                                                    <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-200">
                                                        <div class="flex items-center mb-3">
                                                            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2-2H5a2 2 0 01-2-2V9z"></path>
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                            </svg>
                                                            <h4 class="text-lg font-semibold text-blue-800">
                                                                {{ $notification->type === 'meeting_started_verified' ? 'Meeting Start Photos' : 'Meeting End Photos' }}
                                                            </h4>
                                                        </div>

                                                        <p class="text-sm text-blue-700 mb-4">
                                                            {{ $notification->body ?? $notification->message }}
                                                        </p>

                                                        @if (isset($notification->data['client_photo']) && isset($notification->data['provider_photo']))
                                                            <!-- Meeting Verification Photos - Single Horizontal Row -->
                                                            <div class="flex space-x-3 overflow-x-auto">
                                                                <!-- Client Photo -->
                                                                <div class="flex-shrink-0 bg-white rounded-lg p-2 border border-gray-200 min-w-[120px]">
                                                                    <div class="flex items-center mb-1">
                                                                        <div class="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mr-1">
                                                                            <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                                            </svg>
                                                                        </div>
                                                                        <h6 class="text-xs font-medium text-gray-800">{{ $notification->data['client_name'] ?? 'Client' }}</h6>
                                                                    </div>
                                                                    <img src="{{ $notification->data['client_photo'] }}"
                                                                         alt="Client {{ $notification->type === 'meeting_started_verified' ? 'start' : 'end' }} photo"
                                                                         class="w-24 h-24 object-cover rounded border border-gray-200 cursor-pointer hover:opacity-90 transition-opacity"
                                                                         onclick="openImageModal('{{ $notification->data['client_photo'] }}', '{{ $notification->data['client_name'] ?? 'Client' }} {{ $notification->type === 'meeting_started_verified' ? 'Start' : 'End' }} Photo')">
                                                                </div>

                                                                <!-- Provider Photo -->
                                                                <div class="flex-shrink-0 bg-white rounded-lg p-2 border border-gray-200 min-w-[120px]">
                                                                    <div class="flex items-center mb-1">
                                                                        <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-1">
                                                                            <svg class="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                                            </svg>
                                                                        </div>
                                                                        <h6 class="text-xs font-medium text-gray-800">{{ $notification->data['provider_name'] ?? 'Provider' }}</h6>
                                                                    </div>
                                                                    <img src="{{ $notification->data['provider_photo'] }}"
                                                                         alt="Provider {{ $notification->type === 'meeting_started_verified' ? 'start' : 'end' }} photo"
                                                                         class="w-24 h-24 object-cover rounded border border-gray-200 cursor-pointer hover:opacity-90 transition-opacity"
                                                                         onclick="openImageModal('{{ $notification->data['provider_photo'] }}', '{{ $notification->data['provider_name'] ?? 'Provider' }} {{ $notification->type === 'meeting_started_verified' ? 'Start' : 'End' }} Photo')">
                                                                </div>
                                                            </div>

                                                            <div class="mt-3 text-xs text-blue-600 text-center">
                                                                Meeting Date: {{ $notification->data['meeting_date'] ?? 'N/A' }}
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            @elseif ($notification->type === 'refund_processed')
                                                <!-- Short message with expand button -->
                                                <div class="flex items-start gap-2">
                                                    <p class="text-sm text-gray-700 mb-2 flex-1">
                                                        {{ Str::limit($notification->message ?? $notification->body, 80) }}
                                                    </p>
                                                    <button onclick="toggleDetails('notification-{{ $notification->id }}')"
                                                            class="text-blue-600 hover:text-blue-800 flex-shrink-0 mt-1">
                                                        <svg id="arrow-notification-{{ $notification->id }}" class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                        </svg>
                                                    </button>
                                                </div>

                                                <!-- Full details (hidden by default) -->
                                                <div id="details-notification-{{ $notification->id }}" class="hidden mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                                    <p class="text-sm text-gray-700 mb-3">
                                                        {{ $notification->body ?? $notification->message }}
                                                    </p>
                                                    <div class="bg-white rounded-lg p-3">
                                                        <div class="flex items-center mb-2">
                                                            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                            </svg>
                                                            <span class="font-medium text-blue-800">Refund Details</span>
                                                        </div>
                                                        <p class="text-sm text-blue-700 mb-1">
                                                            Amount refunded: ₹{{ $notification->data['refund_amount'] ?? 'N/A' }}
                                                        </p>
                                                        <p class="text-xs text-blue-600">
                                                            Current wallet balance: ₹{{ $notification->data['wallet_balance'] ?? 'N/A' }}
                                                        </p>
                                                    </div>
                                                </div>
                                            @elseif (in_array($notification->type, ['withdrawal_requested', 'withdrawal_completed', 'withdrawal_cancelled']))
                                                <!-- Simple Withdrawal Notifications (no expand) -->
                                                @php
                                                    // Determine payment method type
                                                    $paymentMethod = 'Bank';
                                                    if (isset($notification->data['bank_account']) && str_contains(strtolower($notification->data['bank_account']), 'gpay')) {
                                                        $paymentMethod = 'G-Pay';
                                                    } elseif (isset($notification->data['bank_account']) && str_contains(strtolower($notification->data['bank_account']), 'g-pay')) {
                                                        $paymentMethod = 'G-Pay';
                                                    }

                                                    // Create simplified message
                                                    $amount = isset($notification->data['amount']) ? '₹' . number_format($notification->data['amount'], 0) : '';

                                                    if ($notification->type === 'withdrawal_requested') {
                                                        $simpleMessage = "Your withdrawal request of {$amount} via {$paymentMethod} has been submitted to admin";
                                                    } elseif ($notification->type === 'withdrawal_completed') {
                                                        $simpleMessage = "Your withdrawal of {$amount} via {$paymentMethod} has been completed successfully";
                                                    } elseif ($notification->type === 'withdrawal_cancelled') {
                                                        $simpleMessage = "Your withdrawal request of {$amount} via {$paymentMethod} has been cancelled";
                                                    } else {
                                                        $simpleMessage = $notification->message ?? $notification->body;
                                                    }
                                                @endphp
                                                <p class="text-sm text-gray-700 mb-2">
                                                    {{ $simpleMessage }}
                                                </p>
                                            @elseif ($notification->type === 'withdrawal_rejected')
                                                <!-- Withdrawal Rejection with Reason -->
                                                <div class="flex items-start gap-2">
                                                    @php
                                                        $paymentMethod = 'Bank';
                                                        if (isset($notification->data['bank_account']) && str_contains(strtolower($notification->data['bank_account']), 'gpay')) {
                                                            $paymentMethod = 'G-Pay';
                                                        } elseif (isset($notification->data['bank_account']) && str_contains(strtolower($notification->data['bank_account']), 'g-pay')) {
                                                            $paymentMethod = 'G-Pay';
                                                        }

                                                        $amount = isset($notification->data['amount']) ? '₹' . number_format($notification->data['amount'], 0) : '';
                                                        $simpleMessage = "Your withdrawal request of {$amount} via {$paymentMethod} has been rejected";
                                                    @endphp
                                                    <p class="text-sm text-gray-700 mb-2 flex-1">
                                                        {{ $simpleMessage }}
                                                    </p>
                                                    @if (isset($notification->data['failure_reason']))
                                                        <button onclick="toggleDetails('notification-{{ $notification->id }}')"
                                                                class="text-blue-600 hover:text-blue-800 flex-shrink-0 mt-1">
                                                            <svg id="arrow-notification-{{ $notification->id }}" class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                            </svg>
                                                        </button>
                                                    @endif
                                                </div>

                                                @if (isset($notification->data['failure_reason']))
                                                    <!-- Rejection Reason (hidden by default) -->
                                                    <div id="details-notification-{{ $notification->id }}" class="hidden mt-2">
                                                        <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                                                            <div class="flex items-start">
                                                                <svg class="w-4 h-4 text-red-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                                </svg>
                                                                <div>
                                                                    <p class="text-sm font-medium text-red-800 mb-1">Rejection Reason:</p>
                                                                    <p class="text-sm text-red-700">{{ $notification->data['failure_reason'] }}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                            @else
                                                <!-- Short message with expand button -->
                                                <div class="flex items-start gap-2">
                                                    <p class="text-sm text-gray-700 mb-2 flex-1">
                                                        {{ Str::limit($notification->message ?? $notification->body, 80) }}
                                                    </p>
                                                    @if (strlen($notification->message ?? $notification->body) > 80)
                                                        <button onclick="toggleDetails('notification-{{ $notification->id }}')"
                                                                class="text-blue-600 hover:text-blue-800 flex-shrink-0 mt-1">
                                                            <svg id="arrow-notification-{{ $notification->id }}" class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                            </svg>
                                                        </button>
                                                    @endif
                                                </div>

                                                <!-- Full details (hidden by default) -->
                                                @if (strlen($notification->message ?? $notification->body) > 80)
                                                    <div id="details-notification-{{ $notification->id }}" class="hidden mt-2 p-3 bg-gray-50 rounded-lg">
                                                        <p class="text-sm text-gray-700">
                                                            {{ $notification->body ?? $notification->message }}
                                                        </p>
                                                    </div>
                                                @endif
                                            @endif

                                            <p class="text-xs text-gray-500">
                                                {{ $notification->created_at->format('M d, Y H:i') }}
                                            </p>
                                        </div>

                                        <div class="ml-3 flex flex-col space-y-2">
                                            @if (!$notification->is_read)
                                                <form action="{{ route('notifications.mark-as-read', $notification) }}" method="POST">
                                                    @csrf
                                                    <button type="submit" class="text-xs text-indigo-600 hover:text-indigo-900 font-medium">
                                                        Mark Read
                                                    </button>
                                                </form>
                                            @endif

                                            @if ($notification->type === 'booking_request' && isset($notification->data['booking_id']))
                                                @php
                                                    $actionBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id']);
                                                    $isBlocked = $actionBooking ? \App\Models\BlockedUser::isBlocked(Auth::user()->id, $actionBooking->client_id) : false;
                                                @endphp

                                                @if ($actionBooking && $actionBooking->provider_status === 'pending' && !$isBlocked && Auth::user()->id === $actionBooking->provider_id && $actionBooking->status !== 'auto_cancelled' && $actionBooking->booking_date > now())
                                                    <!-- Pending booking - show action buttons -->
                                                    <div class="flex space-x-2">
                                                        <button onclick="acceptBooking({{ $notification->data['booking_id'] }})"
                                                                class="px-3 py-1 text-xs font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors"
                                                                style="background-color: #059669; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#047857'"
                                                                onmouseout="this.style.backgroundColor='#059669'">
                                                            Accept
                                                        </button>
                                                        <button onclick="showRejectModal({{ $notification->data['booking_id'] }})"
                                                                class="px-3 py-1 text-xs font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors"
                                                                style="background-color: #dc2626; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#b91c1c'"
                                                                onmouseout="this.style.backgroundColor='#dc2626'">
                                                            Reject
                                                        </button>
                                                        <button onclick="blockClient({{ $notification->data['booking_id'] }})"
                                                                class="px-3 py-1 text-xs font-medium text-white bg-gray-600 hover:bg-gray-700 rounded-md transition-colors"
                                                                style="background-color: #4b5563; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#374151'"
                                                                onmouseout="this.style.backgroundColor='#4b5563'">
                                                            Block
                                                        </button>
                                                    </div>
                                                @elseif ($actionBooking && $actionBooking->provider_status === 'accepted' && Auth::user()->id === $actionBooking->provider_id)
                                                    <!-- Accepted booking - show status -->
                                                    <div class="mt-2 p-2 bg-green-50 rounded-lg border border-green-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-green-800">Booking Accepted</span>
                                                        </div>
                                                    </div>
                                                @elseif ($actionBooking && $actionBooking->provider_status === 'rejected' && $isBlocked && Auth::user()->id === $actionBooking->provider_id)
                                                    <!-- Blocked user - show unblock option -->
                                                    <div class="mt-2 p-2 bg-gray-50 rounded-lg border border-gray-200">
                                                        <div class="flex items-center justify-between">
                                                            <div class="flex items-center">
                                                                <svg class="w-4 h-4 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                                                </svg>
                                                                <span class="text-sm font-medium text-gray-800">User Blocked</span>
                                                            </div>
                                                            <button onclick="unblockUser({{ $actionBooking->client_id }})"
                                                                    class="px-3 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                                                                    style="background-color: #2563eb; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                    onmouseover="this.style.backgroundColor='#1d4ed8'"
                                                                    onmouseout="this.style.backgroundColor='#2563eb'">
                                                                Unblock
                                                            </button>
                                                        </div>
                                                    </div>
                                                @elseif ($actionBooking && $actionBooking->status === 'auto_cancelled' && Auth::user()->id === $actionBooking->provider_id)
                                                    <!-- Auto-cancelled booking - show status -->
                                                    <div class="mt-2 p-2 bg-orange-50 rounded-lg border border-orange-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-orange-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-orange-800">Auto-Cancelled</span>
                                                            <div class="relative inline-block ml-2 group">
                                                                <span class="inline-flex items-center justify-center w-4 h-4 text-xs font-bold text-white bg-orange-600 rounded-full cursor-help">
                                                                    i
                                                                </span>
                                                                <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs text-white bg-gray-800 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                                                                    Booking was automatically cancelled due to no response before meeting time.
                                                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                @elseif ($actionBooking && $actionBooking->provider_status === 'rejected' && Auth::user()->id === $actionBooking->provider_id)
                                                    <!-- Rejected booking - show status -->
                                                    <div class="mt-2 p-2 bg-red-50 rounded-lg border border-red-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-red-800">Booking Rejected</span>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endif

                                            @if (in_array($notification->type, ['booking_accepted', 'booking_request', 'payment_received']) && isset($notification->data['booking_id']))
                                                @php
                                                    $chatBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id']);
                                                    if ($chatBooking) {
                                                        $bookingEndTime = \Carbon\Carbon::parse($chatBooking->booking_date)->addHours((float) $chatBooking->duration_hours);
                                                        $isMeetingCompleted = $chatBooking->isMeetingCompleted() || $bookingEndTime->isPast();
                                                        $isBookingValid = $chatBooking->payment_status === 'paid' && $chatBooking->provider_status === 'accepted';

                                                        $showChatButton = $isBookingValid && !$isMeetingCompleted && $bookingEndTime->isFuture();
                                                        $showReviewButton = $isBookingValid && $isMeetingCompleted && \App\Helpers\FeatureHelper::isRatingReviewSystemActive() && \App\Models\RatingReview::canReviewBooking($chatBooking->id, Auth::user()->id);
                                                        $showReviewSubmitted = $isBookingValid && $isMeetingCompleted && \App\Helpers\FeatureHelper::isRatingReviewSystemActive() && \App\Models\RatingReview::hasUserReviewed($chatBooking->id, Auth::user()->id);

                                                        // Get other user name for review
                                                        $otherUser = $chatBooking->client_id === Auth::user()->id ? $chatBooking->provider : $chatBooking->client;
                                                        $otherUserName = $otherUser ? $otherUser->name : 'Unknown User';
                                                    } else {
                                                        $showChatButton = false;
                                                        $showReviewButton = false;
                                                        $otherUserName = '';
                                                    }
                                                @endphp

                                                @if ($showChatButton)
                                                    <div class="flex space-x-2">
                                                        <button onclick="openChat({{ $notification->data['booking_id'] }})"
                                                                class="px-4 py-2 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                                                                style="background-color: #2563eb; color: white; padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#1d4ed8'"
                                                                onmouseout="this.style.backgroundColor='#2563eb'">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                                            </svg>
                                                            Chat Now
                                                        </button>
                                                    </div>
                                                @elseif ($showReviewButton)
                                                    <div class="flex space-x-2">
                                                        <button onclick="rateExperienceModal({{ $notification->data['booking_id'] }})"
                                                                class="px-4 py-2 text-xs font-medium text-white bg-yellow-600 hover:bg-yellow-700 rounded-md transition-colors"
                                                                style="background-color: #d97706; color: white; padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#b45309'"
                                                                onmouseout="this.style.backgroundColor='#d97706'">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                                            </svg>
                                                            Rate & Review
                                                        </button>
                                                    </div>
                                                @elseif ($showReviewSubmitted)
                                                    <div class="flex space-x-2">
                                                        <div class="px-4 py-2 text-xs font-medium text-green-700 bg-green-100 rounded-md border border-green-200 flex items-center"
                                                             style="padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px;">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                            </svg>
                                                            Review Submitted
                                                        </div>
                                                    </div>
                                                @endif
                                            @endif

                                            <!-- Cancel and Update Booking Buttons for Pending Client Bookings -->
                                            @if (isset($notification->data['booking_id']))
                                                @php
                                                    $actionBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id']);
                                                    $showButtons = $actionBooking &&
                                                                  $actionBooking->client_id === Auth::user()->id &&
                                                                  $actionBooking->provider_status === 'pending' &&
                                                                  $actionBooking->payment_status === 'paid' &&
                                                                  $actionBooking->status !== 'cancelled' &&
                                                                  $actionBooking->booking_date > now(); // Cannot update after meeting start time
                                                @endphp

                                                @if ($showButtons)
                                                    <div class="mt-2 flex space-x-2">
                                                        <button onclick="showCancelModal({{ $notification->data['booking_id'] }})"
                                                                class="px-3 py-1 text-xs font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors"
                                                                style="background-color: #dc2626; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#b91c1c'"
                                                                onmouseout="this.style.backgroundColor='#dc2626'">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                            </svg>
                                                            Cancel Booking
                                                        </button>

                                                        <button onclick="redirectToUpdateBooking({{ $notification->data['booking_id'] }})"
                                                                class="px-3 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                                                                style="background-color: #2563eb; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#1d4ed8'"
                                                                onmouseout="this.style.backgroundColor='#2563eb'">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                            </svg>
                                                            Update Booking
                                                        </button>
                                                    </div>
                                                @endif



                                            <!-- Dispute/No-Show Button for Eligible Bookings -->
                                            @if (isset($notification->data['booking_id']) && $notification->type === 'meeting_completed_no_photos')
                                                @php
                                                    $disputeBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id']);
                                                    $canRaiseDispute = $disputeBooking &&
                                                                      $disputeBooking->client_id === Auth::user()->id &&
                                                                      $disputeBooking->canRaiseNoShowDispute();
                                                    $hasDispute = $disputeBooking && $disputeBooking->escrow_status === 'disputed';
                                                @endphp

                                                @if ($canRaiseDispute)
                                                    <div class="mt-2">
                                                        <button onclick="showDisputeModal({{ $notification->data['booking_id'] }})"
                                                                class="px-3 py-1 text-xs font-medium text-white bg-orange-600 hover:bg-orange-700 rounded-md transition-colors"
                                                                style="background-color: #ea580c; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#c2410c'"
                                                                onmouseout="this.style.backgroundColor='#ea580c'">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                            </svg>
                                                            {{ $notification->data['other_user_name'] ?? 'They' }} Don't Come
                                                        </button>
                                                    </div>
                                                @elseif ($hasDispute)
                                                    <div class="mt-2 p-2 bg-orange-50 rounded-lg border border-orange-200">
                                                        <div class="flex items-center justify-between">
                                                            <div class="flex items-center">
                                                                <svg class="w-4 h-4 text-orange-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                                </svg>
                                                                <span class="text-sm font-medium text-orange-800">{{ $disputeBooking->dispute_status_display }}</span>
                                                            </div>
                                                            <button onclick="viewDisputeDetails({{ $notification->data['booking_id'] }})"
                                                                    class="text-xs text-orange-600 hover:text-orange-800 underline">
                                                                View Details
                                                            </button>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endif
                                            @endif

                                            <!-- Review Action Buttons -->
                                            @if ($notification->type === 'review_reminder' && isset($notification->data['booking_id']))
                                                @php
                                                    $reviewBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id']);
                                                    $canReview = $reviewBooking && \App\Models\RatingReview::canReviewBooking($reviewBooking->id, Auth::user()->id);
                                                    $hasReviewedAlready = $reviewBooking && \App\Models\RatingReview::hasUserReviewed($reviewBooking->id, Auth::user()->id);
                                                @endphp

                                                @if ($canReview)
                                                    <div class="mt-2">
                                                        <button onclick="rateExperienceModal({{ $notification->data['booking_id'] }})"
                                                                class="px-4 py-2 text-xs font-medium text-white bg-yellow-600 hover:bg-yellow-700 rounded-md transition-colors"
                                                                style="background-color: #d97706; color: white; padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#b45309'"
                                                                onmouseout="this.style.backgroundColor='#d97706'">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                                            </svg>
                                                            Rate & Review
                                                        </button>
                                                    </div>
                                                @elseif ($hasReviewedAlready)
                                                    <div class="mt-2">
                                                        <div class="px-4 py-2 text-xs font-medium text-green-700 bg-green-100 rounded-md border border-green-200 flex items-center"
                                                             style="padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px;">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                            </svg>
                                                            Review Submitted
                                                        </div>
                                                    </div>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endif

                                            @if ($notification->type === 'review_received' && isset($notification->data['review_id']))
                                                <div class="mt-2">
                                                    <button onclick="openMyReviewsModal()"
                                                            class="px-4 py-2 text-xs font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors"
                                                            style="background-color: #059669; color: white; padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                            onmouseover="this.style.backgroundColor='#047857'"
                                                            onmouseout="this.style.backgroundColor='#059669'">
                                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                        </svg>
                                                        View My Reviews
                                                    </button>
                                                </div>
                                            @endif

                                            <!-- No-Show Detection Notification -->
                                            @if ($notification->type === 'no_show_detected' && isset($notification->data['booking_id']))
                                                @php
                                                    $noShowBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id']);
                                                    $canStillDispute = $noShowBooking && $noShowBooking->canRaiseNoShowDispute();
                                                @endphp

                                                @if ($canStillDispute)
                                                    <div class="mt-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                                                        <div class="flex items-start space-x-3">
                                                            <svg class="w-5 h-5 text-orange-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                            </svg>
                                                            <div class="flex-1">
                                                                <p class="text-sm text-orange-800 mb-2">
                                                                    <strong>Potential No-Show Detected:</strong> If {{ $notification->data['provider_name'] ?? 'the provider' }} didn't show up for your meeting, you can report this incident.
                                                                </p>
                                                                <button onclick="showDisputeModal({{ $notification->data['booking_id'] }})"
                                                                        class="px-3 py-1 text-xs font-medium text-white bg-orange-600 hover:bg-orange-700 rounded-md transition-colors"
                                                                        style="background-color: #ea580c; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                        onmouseover="this.style.backgroundColor='#c2410c'"
                                                                        onmouseout="this.style.backgroundColor='#ea580c'">
                                                                    {{ $notification->data['provider_name'] ?? 'They' }} Didn't Come
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @else
                                                    <div class="mt-2 p-2 bg-gray-50 rounded-lg border border-gray-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-gray-800">Issue Already Reported or Resolved</span>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endif

                                            <!-- Dispute Status Notifications -->
                                            @if (in_array($notification->type, ['dispute_submitted', 'dispute_updated', 'dispute_resolved']) && isset($notification->data['booking_id']))
                                                <div class="mt-2">
                                                    <button onclick="viewDisputeDetails({{ $notification->data['booking_id'] }})"
                                                            class="px-3 py-1 text-xs font-medium text-blue-600 hover:text-blue-800 underline">
                                                        View Dispute Details
                                                    </button>
                                                </div>
                                            @endif

                                            <!-- Meeting Completion Action Buttons -->
                                            @if (in_array($notification->type, ['meeting_completed_rate', 'meeting_completed_no_photos', 'meeting_completed_partial']) && isset($notification->data['booking_id']))
                                                @php
                                                    $completionBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id']);
                                                    $actionType = $notification->data['action_type'] ?? '';

                                                    // Check review status for all meeting completion notifications
                                                    $canReviewCompletion = \App\Models\RatingReview::canReviewBooking($completionBooking->id, Auth::user()->id);
                                                    $hasReviewedCompletion = \App\Models\RatingReview::hasUserReviewed($completionBooking->id, Auth::user()->id);
                                                @endphp

                                                @if ($completionBooking)
                                                    <div class="mt-2">
                                                        @if ($notification->type === 'meeting_completed_rate' || $actionType === 'rate_experience')
                                                            @if ($canReviewCompletion)
                                                                <!-- Rate Experience Button -->
                                                                <button onclick="rateExperienceModal({{ $completionBooking->id }})"
                                                                        class="px-4 py-2 text-xs font-medium text-white bg-yellow-600 hover:bg-yellow-700 rounded-md transition-colors"
                                                                        style="background-color: #d97706; color: white; padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                        onmouseover="this.style.backgroundColor='#b45309'"
                                                                        onmouseout="this.style.backgroundColor='#d97706'">
                                                                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                                                    </svg>
                                                                    Rate & Review
                                                                </button>
                                                            @elseif ($hasReviewedCompletion)
                                                                <!-- Review Already Submitted -->
                                                                <div class="px-4 py-2 text-xs font-medium text-green-700 bg-green-100 rounded-md border border-green-200 flex items-center"
                                                                     style="padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px;">
                                                                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                                    </svg>
                                                                    Review Submitted
                                                                </div>
                                                            @elseif ($notification->type === 'meeting_completed_rate')
                                                                <!-- Fallback Rate Experience Button for Rate Your Experience notifications -->
                                                                <button onclick="rateExperienceModal({{ $completionBooking->id }})"
                                                                        class="px-4 py-2 text-xs font-medium text-white bg-yellow-600 hover:bg-yellow-700 rounded-md transition-colors"
                                                                        style="background-color: #d97706; color: white; padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                        onmouseover="this.style.backgroundColor='#b45309'"
                                                                        onmouseout="this.style.backgroundColor='#d97706'">
                                                                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                                                    </svg>
                                                                    Rate & Review
                                                                </button>
                                                                <div class="mt-1 px-2 py-1 text-xs text-gray-500 bg-yellow-50 rounded border border-yellow-200"
                                                                     style="font-size: 10px;">
                                                                    Note: You can still rate your experience even if some conditions aren't met.
                                                                </div>
                                                            @else
                                                                <!-- Show reason why can't review with debugging info -->
                                                                <div class="px-4 py-2 text-xs font-medium text-gray-600 bg-gray-100 rounded-md border border-gray-200"
                                                                     style="padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px;">
                                                                    {{ \App\Models\RatingReview::getCannotReviewReason($completionBooking->id, Auth::user()->id) }}
                                                                </div>

                                                                <!-- Debug Info (remove in production) -->
                                                                @if(config('app.debug'))
                                                                    <div class="mt-1 px-2 py-1 text-xs text-gray-500 bg-gray-50 rounded border"
                                                                         style="font-size: 10px;">
                                                                        Debug: Rating System Active: {{ \App\Helpers\FeatureHelper::isRatingReviewSystemActive() ? 'Yes' : 'No' }} |
                                                                        Booking Status: {{ $completionBooking->provider_status ?? 'N/A' }} |
                                                                        Payment: {{ $completionBooking->payment_status ?? 'N/A' }} |
                                                                        Action Type: {{ $actionType ?? 'N/A' }} |
                                                                        Notification Type: {{ $notification->type }}
                                                                    </div>
                                                                @endif
                                                            @endif
                                                        @elseif ($actionType === 'report_issue')
                                                            <!-- Report Issue Button -->
                                                            <button onclick="reportNoShow({{ $completionBooking->id }})"
                                                                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
                                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                                </svg>
                                                                {{ $notification->data['other_user_name'] ?? 'They' }} Don't Come
                                                            </button>
                                                        @elseif ($actionType === 'resolve_meeting')
                                                            <!-- Resolve Meeting Button -->
                                                            <button onclick="endMeetingManually({{ $completionBooking->id }})"
                                                                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 rounded-lg transition-colors">
                                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                                                </svg>
                                                                Resolve Meeting
                                                            </button>
                                                        @endif
                                                    </div>
                                                @endif
                                            @endif

                                            <!-- Couple Activity Action Buttons -->
                                            @if ($notification->type === 'couple_activity' && isset($notification->data['request_id']))
                                                @php
                                                    $coupleRequest = \App\Models\CoupleActivityRequest::find($notification->data['request_id']);
                                                @endphp

                                                @if ($coupleRequest && $coupleRequest->status === 'pending' && $coupleRequest->partner_id === Auth::user()->id)
                                                    <!-- Pending couple activity request - show action buttons -->
                                                    <div class="flex space-x-2 mt-2">
                                                        <form action="{{ route('couple-activity.approve', $coupleRequest) }}" method="POST" class="inline">
                                                            @csrf
                                                            <button type="submit"
                                                                    class="px-3 py-1 text-xs font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors"
                                                                    style="background-color: #059669; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                    onmouseover="this.style.backgroundColor='#047857'"
                                                                    onmouseout="this.style.backgroundColor='#059669'">
                                                                ✓ Accept
                                                            </button>
                                                        </form>
                                                        <form action="{{ route('couple-activity.reject', $coupleRequest) }}" method="POST" class="inline">
                                                            @csrf
                                                            <button type="submit"
                                                                    class="px-3 py-1 text-xs font-medium text-white bg-yellow-600 hover:bg-yellow-700 rounded-md transition-colors"
                                                                    style="background-color: #d97706; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                    onmouseover="this.style.backgroundColor='#b45309'"
                                                                    onmouseout="this.style.backgroundColor='#d97706'">
                                                                ⚠ Decline
                                                            </button>
                                                        </form>
                                                        <form action="{{ route('couple-activity.block', $coupleRequest) }}" method="POST" class="inline">
                                                            @csrf
                                                            <button type="submit"
                                                                    class="px-3 py-1 text-xs font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors"
                                                                    style="background-color: #dc2626; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                    onmouseover="this.style.backgroundColor='#b91c1c'"
                                                                    onmouseout="this.style.backgroundColor='#dc2626'"
                                                                    onclick="return confirm('Are you sure you want to block this user?')">
                                                                🚫 Block
                                                            </button>
                                                        </form>
                                                    </div>
                                                @elseif ($coupleRequest && $coupleRequest->status === 'approved')
                                                    <!-- Approved couple activity request - show status -->
                                                    <div class="mt-2 p-2 bg-green-50 rounded-lg border border-green-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-green-800">Request Accepted</span>
                                                        </div>
                                                    </div>
                                                @elseif ($coupleRequest && $coupleRequest->status === 'rejected')
                                                    <!-- Rejected couple activity request - show status -->
                                                    <div class="mt-2 p-2 bg-red-50 rounded-lg border border-red-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-red-800">Request Declined</span>
                                                        </div>
                                                    </div>
                                                @elseif ($coupleRequest && $coupleRequest->status === 'blocked')
                                                    <!-- Blocked couple activity request - show status -->
                                                    <div class="mt-2 p-2 bg-gray-50 rounded-lg border border-gray-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-gray-800">User Blocked</span>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endif

                                            <!-- Sugar Partner Exchange Action Buttons -->
                                            @if (in_array($notification->type, ['sugar_partner_exchange_initiated', 'sugar_partner_profiles_viewable', 'sugar_partner_response_received', 'sugar_partner_match_success', 'sugar_partner_mismatch', 'sugar_partner_response_pending', 'sugar_partner_hard_reject_converted', 'sugar_partner_rejection_cleared']) && isset($notification->data['action_url']))
                                                <div class="mt-2">
                                                    <a href="{{ $notification->data['action_url'] }}"
                                                       class="inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-pink-600 hover:bg-pink-700 rounded-md transition-colors"
                                                       style="background-color: #db2777; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; text-decoration: none; transition: all 0.2s;"
                                                       onmouseover="this.style.backgroundColor='#be185d'"
                                                       onmouseout="this.style.backgroundColor='#db2777'">
                                                        @if ($notification->type === 'sugar_partner_exchange_initiated')
                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                            </svg>
                                                        @elseif ($notification->type === 'sugar_partner_profiles_viewable')
                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                            </svg>
                                                        @else
                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                            </svg>
                                                        @endif
                                                        {{ $notification->data['action_text'] ?? 'View Details' }}
                                                    </a>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach

                            <div class="mt-4">
                                {{ $notifications->links() }}
                            </div>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
                            <p class="mt-1 text-sm text-gray-500">
                                You don't have any notifications yet.
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Reject Modal -->
    <div id="rejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Reject Booking</h3>
                <form id="rejectForm">
                    <div class="mb-4">
                        <label for="rejectionReason" class="block text-sm font-medium text-gray-700 mb-2">
                            Reason for rejection:
                        </label>
                        <textarea id="rejectionReason" name="reason" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                                  placeholder="Please provide a reason for rejecting this booking..."
                                  required></textarea>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeRejectModal()"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors">
                            Reject Booking
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Cancel Booking Modal -->
    <div id="cancelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Cancel Booking</h3>
                <div class="mb-4">
                    <p class="text-sm text-gray-700 mb-3">
                        Are you sure you want to cancel this booking? The full amount will be refunded to your wallet.
                    </p>
                    <div id="cancelBookingDetails" class="bg-gray-50 rounded-lg p-3 mb-3">
                        <!-- Booking details will be populated here -->
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeCancelModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors">
                        Keep Booking
                    </button>
                    <button type="button" onclick="confirmCancelBooking()" id="confirmCancelBtn"
                            class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors">
                        <span id="cancelBtnText">Cancel Booking</span>
                        <span id="cancelBtnLoading" class="hidden">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Cancelling...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Booking Modal -->
    <div id="updateBookingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Update Booking</h3>

                <div id="currentBookingInfo"></div>

                <form class="space-y-4">
                    <div>
                        <label for="updateBookingDate" class="block text-sm font-medium text-gray-700 mb-1">New Date</label>
                        <input type="date" id="updateBookingDate" name="booking_date"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               min="{{ date('Y-m-d') }}" required>
                    </div>

                    <div>
                        <label for="updateBookingTime" class="block text-sm font-medium text-gray-700 mb-1">New Time</label>
                        <input type="time" id="updateBookingTime" name="booking_time"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               required>
                    </div>

                    <div>
                        <label for="updateDurationHours" class="block text-sm font-medium text-gray-700 mb-1">Duration (hours)</label>
                        <select id="updateDurationHours" name="duration_hours"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required>
                            <option value="0.5">30 minutes</option>
                            <option value="1">1 hour</option>
                            <option value="1.5">1.5 hours</option>
                            <option value="2">2 hours</option>
                            <option value="2.5">2.5 hours</option>
                            <option value="3">3 hours</option>
                            <option value="4">4 hours</option>
                            <option value="5">5 hours</option>
                            <option value="6">6 hours</option>
                            <option value="8">8 hours</option>
                            <option value="10">10 hours</option>
                            <option value="12">12 hours</option>
                        </select>
                    </div>

                    <div>
                        <label for="updateLocation" class="block text-sm font-medium text-gray-700 mb-1">Meeting Location</label>
                        <input type="text" id="updateLocation" name="location"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Enter meeting location" required>
                    </div>

                    <div class="bg-blue-50 p-3 rounded-lg">
                        <div class="flex justify-between items-center text-sm">
                            <span class="text-gray-600">New Total Amount:</span>
                            <span class="font-semibold text-blue-600">₹<span id="updateTotalAmount">0</span></span>
                        </div>
                        <div class="flex justify-between items-center text-xs text-gray-500 mt-1">
                            <span>Duration:</span>
                            <span id="updateTotalHours">0 hours</span>
                        </div>
                    </div>
                </form>

                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeUpdateBookingModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors">
                        Cancel
                    </button>
                    <button type="button" id="updateBookingBtn" onclick="submitUpdateBooking()"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
                        <span id="updateBtnText">Update Booking</span>
                        <span id="updateBtnLoading" class="hidden">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Updating...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Camera Modal for Meeting Verification -->
    <div id="cameraModal" class="fixed inset-0 bg-black bg-opacity-80 overflow-y-auto h-full w-full hidden z-50 flex items-center justify-center">
        <div class="relative mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 id="cameraModalTitle" class="text-lg font-medium text-gray-900">Take Meeting Photo</h3>
                    <button onclick="closeCameraModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div id="cameraInstructions" class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div id="cameraPermissionRequired" >
                        <button id="requestCameraPermissionBtn" onclick="requestCameraPermission()"
                                class="w-full px-4 py-2 mb-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                            📷 Grant Camera Permission
                        </button>
                        <button id="requestLocationPermissionBtn" onclick="requestLocationPermission()"
                                class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            📍 Grant Location Permission
                        </button>
                        <p class="text-xs text-gray-600 mt-2">Both permissions are required for meeting verification.</p>
                    </div>

                </div>

                <!-- Camera Permission Required -->
                


                <!-- Location Status -->
                <div id="locationStatus" class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span id="locationText" class="text-sm text-yellow-800">Getting location...</span>
                    </div>
                </div>

                <!-- Photo Instructions -->
                <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p class="text-sm text-blue-800 font-medium">
                        📸 Important: Both participants must be visible in the same photo together
                    </p>
                </div>

                <!-- Camera Preview -->
                <div id="cameraContainer" class="mb-4">
                    <video id="cameraPreview" class="w-full h-64 bg-gray-200 rounded-lg object-cover" autoplay playsinline></video>
                    <canvas id="photoCanvas" class="hidden"></canvas>
                </div>

                <!-- Captured Photo Preview -->
                <div id="photoPreview" class="mb-4 hidden">
                    <img id="capturedPhoto" class="w-full h-64 bg-gray-200 rounded-lg object-cover" alt="Captured photo">
                </div>

                <!-- Camera Controls -->
                <div class="flex justify-center space-x-3">
                    <button id="startCameraBtn" onclick="startCamera()"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
                        Start Camera
                    </button>
                    <button id="captureBtn" onclick="capturePhoto()"
                            class="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors hidden">
                        Capture Photo
                    </button>
                    <button id="retakeBtn" onclick="retakePhoto()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors hidden">
                        Retake
                    </button>
                    <button id="uploadBtn" onclick="uploadPhoto()"
                            class="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md transition-colors hidden">
                        <span id="uploadBtnText">Upload Photo</span>
                        <span id="uploadBtnLoading" class="hidden">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Uploading...
                        </span>
                    </button>
                </div>

                <div id="cameraError" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg hidden">
                    <p id="cameraErrorText" class="text-sm text-red-800"></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal for Meeting Photos -->
    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-90 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative min-h-screen flex items-center justify-center p-4">
            <div class="relative max-w-4xl max-h-full">
                <!-- Close Button -->
                <button onclick="closeImageModal()"
                        class="absolute top-4 right-4 z-10 text-white hover:text-gray-300 bg-black bg-opacity-50 rounded-full p-2 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>

                <!-- Image Title -->
                <div class="absolute top-4 left-4 z-10 bg-black bg-opacity-50 text-white px-3 py-1 rounded-lg">
                    <h3 id="modalTitle" class="text-sm font-medium"></h3>
                </div>

                <!-- Image -->
                <img id="modalImage"
                     class="max-w-full max-h-screen object-contain rounded-lg shadow-2xl"
                     alt="Meeting verification photo">
            </div>
        </div>
    </div>

    <!-- Dispute Modal -->
    <div id="disputeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Report Provider No-Show</h3>
                    <button onclick="closeDisputeModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                    <p class="text-sm text-orange-800">
                        <strong>Important:</strong> Only report a no-show if the provider failed to attend the scheduled meeting. False reports may result in account restrictions.
                    </p>
                </div>

                <form id="disputeForm" enctype="multipart/form-data">
                    <input type="hidden" id="disputeBookingId" name="booking_id">
                    <input type="hidden" name="dispute_type" value="no_show">

                    <div class="mb-4">
                        <label for="disputeReason" class="block text-sm font-medium text-gray-700 mb-2">
                            Describe what happened: <span class="text-red-500">*</span>
                        </label>
                        <textarea id="disputeReason" name="reason" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                  placeholder="Please provide details about the no-show incident (minimum 10 characters)..."
                                  required minlength="10" maxlength="1000"></textarea>
                        <div class="text-xs text-gray-500 mt-1">
                            <span id="reasonCharCount">0</span>/1000 characters (minimum 10)
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="evidencePhotos" class="block text-sm font-medium text-gray-700 mb-2">
                            Evidence Photos (Optional)
                        </label>
                        <input type="file" id="evidencePhotos" name="evidence_photos[]"
                               accept="image/jpeg,image/png,image/jpg" multiple
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                        <div class="text-xs text-gray-500 mt-1">
                            You can upload up to 5 photos (max 5MB each). Screenshots of messages, location photos, etc.
                        </div>
                        <div id="photoPreviewContainer" class="mt-2 grid grid-cols-3 gap-2 hidden"></div>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeDisputeModal()"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors">
                            Cancel
                        </button>
                        <button type="submit" id="submitDisputeBtn"
                                class="px-4 py-2 text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 rounded-md transition-colors">
                            <span id="disputeBtnText">Submit Report</span>
                            <span id="disputeBtnLoading" class="hidden">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Submitting...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Dispute Details Modal -->
    <div id="disputeDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Dispute Details</h3>
                    <button onclick="closeDisputeDetailsModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div id="disputeDetailsContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Legacy Review Modal removed - now using ratingModal -->

    <!-- My Reviews Modal -->
    <div id="myReviewsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50"
         role="dialog" aria-modal="true" aria-labelledby="myReviewsModalTitle">
        <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-xl">
            <div class="p-6">
                <div class="modal-content">
                <div class="flex justify-between items-center mb-6">
                    <h3 id="myReviewsModalTitle" class="text-xl font-semibold text-gray-900">My Reviews</h3>
                    <button onclick="closeMyReviewsModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Reviews Statistics -->
                <div id="reviewsStats" class="mb-6 p-4 bg-gray-50 rounded-lg hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-gray-900" id="averageRating">0.0</div>
                            <div class="text-sm text-gray-600 mb-2">Average Rating</div>
                            <div id="averageStars" class="flex justify-center"></div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-gray-900" id="totalReviews">0</div>
                            <div class="text-sm text-gray-600">Total Reviews</div>
                        </div>
                    </div>
                </div>

                <!-- Loading State -->
                <div id="reviewsLoading" class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
                    <p class="text-gray-600 mt-3 text-sm">Loading your reviews...</p>
                </div>

                <!-- Reviews Container -->
                <div id="myReviewsContainer" class="space-y-4 max-h-80 overflow-y-auto">
                    <!-- Reviews will be loaded here -->
                </div>

                <!-- Empty State -->
                <div id="noReviewsMessage" class="text-center py-8 hidden">
                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">No Reviews Yet</h4>
                    <p class="text-gray-600">You haven't received any reviews yet. Complete some meetings to start receiving reviews!</p>
                </div>

                <!-- Error State -->
                <div id="reviewsError" class="text-center py-8 hidden">
                    <svg class="w-16 h-16 text-red-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">Failed to Load Reviews</h4>
                    <p class="text-gray-600 mb-4">There was an error loading your reviews. Please try again.</p>
                    <button onclick="loadMyReviews()" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors">
                        Try Again
                    </button>
                </div>

                <!-- Pagination -->
                <div id="reviewsPagination" class="mt-6 flex justify-center hidden">
                    <button id="loadMoreReviews" onclick="loadMoreMyReviews()"
                            class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="loadMoreText">Load More Reviews</span>
                        <span id="loadMoreLoading" class="hidden">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Loading...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Define toggleDetails function immediately to ensure it's available for onclick handlers
        window.toggleDetails = function(notificationId) {
            const detailsDiv = document.getElementById('details-' + notificationId);
            const arrow = document.getElementById('arrow-' + notificationId);

            if (detailsDiv && arrow) {
                if (detailsDiv.classList.contains('hidden')) {
                    detailsDiv.classList.remove('hidden');
                    arrow.style.transform = 'rotate(180deg)';
                } else {
                    detailsDiv.classList.add('hidden');
                    arrow.style.transform = 'rotate(0deg)';
                }
            }
        };

        // Helper functions for user-friendly messages - using our toast component
        function showSuccessMessage(message) {
            showToast(message, 'success');
        }

        function showErrorMessage(message) {
            showToast(message, 'error');
        }

        // Booking action variables
        let currentActionBookingId = null;

        // toggleDetails function moved to top of script section to avoid reference errors

        // Accept booking function
        function acceptBooking(bookingId) {
            if (confirm('Are you sure you want to accept this booking?')) {
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                };



                fetch(`/provider/booking/${bookingId}/accept`, {
                    method: 'POST',
                    headers: headers
                })
                .then(response => {


                    // Handle redirects (301, 302, etc.)
                    if (response.redirected) {

                        showErrorMessage('You were redirected. Please check if you are logged in and try again.');
                        return { success: false, handled: true };
                    }

                    // Handle subscription requirement error (403)
                    if (response.status === 403) {
                        return response.text().then(text => {

                            try {
                                const data = JSON.parse(text);
                                if (data.message && data.message.includes('subscription')) {
                                    showErrorMessage('Active subscription required to accept bookings. Please update your subscription to continue.');
                                    setTimeout(() => {
                                        window.location.href = '/profile?tab=time-spending';
                                    }, 3000);
                                    return { success: false, handled: true };
                                }
                                return data;
                            } catch (e) {
                                // Not JSON, probably HTML error page
                                showErrorMessage('Access denied. Please check your subscription status and try again.');
                                return { success: false, handled: true };
                            }
                        });
                    }

                    // Handle other HTTP errors
                    if (!response.ok) {
                        return response.text().then(text => {

                            throw new Error(`HTTP error! status: ${response.status}`);
                        });
                    }

                    // Try to parse as JSON
                    return response.text().then(text => {

                        try {
                            return JSON.parse(text);
                        } catch (e) {
                            console.error('Failed to parse JSON:', text);
                            throw new Error('Invalid JSON response from server');
                        }
                    });
                })
                .then(data => {
                    if (data.handled) {
                        // Already handled subscription error above
                        return;
                    }

                    if (data.success) {
                        showSuccessMessage('Booking accepted successfully!');
                        location.reload();
                    } else {
                        showErrorMessage('Error: ' + (data.message || 'Failed to accept booking'));
                    }
                })
                .catch(error => {
                    console.error('Accept booking error:', error);
                    showErrorMessage('An error occurred while accepting the booking. Please try again.');
                });
            }
        }

        // Show reject modal
        function showRejectModal(bookingId) {
            currentActionBookingId = bookingId;
            document.getElementById('rejectModal').classList.remove('hidden');
        }

        // Close reject modal
        function closeRejectModal() {
            document.getElementById('rejectModal').classList.add('hidden');
            document.getElementById('rejectionReason').value = '';
            currentActionBookingId = null;
        }

        // Handle reject form submission
        document.addEventListener('DOMContentLoaded', function() {
            const rejectForm = document.getElementById('rejectForm');
            if (rejectForm) {
                rejectForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const reason = document.getElementById('rejectionReason').value;
                    if (!reason.trim()) {
                        showErrorMessage('Please provide a reason for rejection.');
                        return;
                    }

                    fetch(`/provider/booking/${currentActionBookingId}/reject`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({ reason: reason })
                    })
                    .then(response => {
                // Handle subscription requirement error (403)
                if (response.status === 403) {
                    return response.json().then(data => {
                        if (data.message && data.message.includes('subscription')) {
                            showErrorMessage('Active subscription required to reject bookings. Please update your subscription to continue.');
                            setTimeout(() => {
                                window.location.href = '/profile?tab=time-spending';
                            }, 3000);
                            return { success: false, handled: true };
                        }
                        return data;
                    });
                }

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return response.json();
            })
            .then(data => {
                if (data.handled) {
                    return;
                }

                if (data.success) {
                    closeRejectModal();
                    showSuccessMessage('Booking rejected successfully!');
                    location.reload();
                } else {
                    showErrorMessage('Error: ' + (data.message || 'Failed to reject booking'));
                }
            })
            .catch(error => {
                console.error('Reject booking error:', error);
                showErrorMessage('An error occurred while rejecting the booking. Please try again.');
                    });
                });
            }
        });

        // Block client function
        function blockClient(bookingId) {
            if (confirm('Are you sure you want to block this client? They will not be able to book you again.')) {
                fetch(`/provider/booking/${bookingId}/block`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    // Handle subscription requirement error (403)
                    if (response.status === 403) {
                        return response.json().then(data => {
                            if (data.message && data.message.includes('subscription')) {
                                showErrorMessage('Active subscription required to block clients. Please update your subscription to continue.');
                                setTimeout(() => {
                                    window.location.href = '/profile?tab=time-spending';
                                }, 3000);
                                return { success: false, handled: true };
                            }
                            return data;
                        });
                    }

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return response.json();
                })
                .then(data => {
                    if (data.handled) {
                        return;
                    }

                    if (data.success) {
                        showSuccessMessage('Client blocked successfully.');
                        location.reload();
                    } else {
                        showErrorMessage('Error: ' + (data.message || 'Failed to block client'));
                    }
                })
                .catch(error => {
                    console.error('Block client error:', error);
                    showErrorMessage('An error occurred while blocking the client. Please try again.');
                });
            }
        }

        // Unblock user function
        function unblockUser(userId) {
            if (confirm('Are you sure you want to unblock this user? They will be able to book you again.')) {
                fetch(`/provider/user/${userId}/unblock`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    // Handle subscription requirement error (403)
                    if (response.status === 403) {
                        return response.json().then(data => {
                            if (data.message && data.message.includes('subscription')) {
                                showErrorMessage('Active subscription required to unblock users. Please update your subscription to continue.');
                                setTimeout(() => {
                                    window.location.href = '/profile?tab=time-spending';
                                }, 3000);
                                return { success: false, handled: true };
                            }
                            return data;
                        });
                    }

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return response.json();
                })
                .then(data => {
                    if (data.handled) {
                        return;
                    }

                    if (data.success) {
                        showSuccessMessage('User unblocked successfully.');
                        location.reload();
                    } else {
                        showErrorMessage('Error: ' + (data.message || 'Failed to unblock user'));
                    }
                })
                .catch(error => {
                    console.error('Unblock user error:', error);
                    showErrorMessage('An error occurred while unblocking the user. Please try again.');
                });
            }
        }

        // Open chat function
        function openChat(bookingId) {
            // Get booking details and redirect to chat
            fetch(`/booking/${bookingId}/chat-details`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success && data.chat_user_id) {
                        // Redirect to chat page with the other user
                        window.location.href = `/chat/${data.chat_user_id}`;
                    } else {
                        showErrorMessage(data.message || 'Unable to open chat. Please try again.');
                    }
                })
                .catch(error => {
                    showErrorMessage('An error occurred. Please try again.');
                });
        }

        // Close modal when clicking outside
        document.addEventListener('DOMContentLoaded', function() {
            const rejectModal = document.getElementById('rejectModal');
            if (rejectModal) {
                rejectModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeRejectModal();
                    }
                });
            }
        });

        // Clear notification badges when page loads (since all notifications are marked as read)
        document.addEventListener('DOMContentLoaded', function() {
            try {
                const desktopBadge = document.getElementById('notification-badge');
                const mobileBadge = document.getElementById('mobile-notification-badge');

                if (desktopBadge) {
                    desktopBadge.style.display = 'none';
                    desktopBadge.classList.remove('has-notifications');
                }
                if (mobileBadge) {
                    mobileBadge.style.display = 'none';
                    mobileBadge.classList.remove('has-notifications');
                }

                // Initialize meeting button permissions
                updateMeetingButtonsOnPageLoad();
            } catch (error) {
                // Error clearing notification badges - handle silently
            }
        });

        // Toggle notification details function is defined above - removing duplicate

        // Cancel booking functionality
        let currentCancelBookingId = null;

        function showCancelModal(bookingId) {
            currentCancelBookingId = bookingId;

            // Fetch booking details
            fetch(`/booking/${bookingId}/details`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const booking = data.booking;
                        const bookingDate = new Date(booking.booking_date);
                        const endTime = new Date(bookingDate.getTime() + (booking.duration_hours * 60 * 60 * 1000));

                        document.getElementById('cancelBookingDetails').innerHTML = `
                            <div class="text-sm">
                                <div class="flex justify-between mb-2">
                                    <span class="font-medium text-gray-700">Provider:</span>
                                    <span class="text-gray-900">${booking.provider.name}</span>
                                </div>
                                <div class="flex justify-between mb-2">
                                    <span class="font-medium text-gray-700">Date:</span>
                                    <span class="text-gray-900">${formatDate(booking.booking_date)}</span>
                                </div>
                                <div class="flex justify-between mb-2">
                                    <span class="font-medium text-gray-700">Time:</span>
                                    <span class="text-gray-900">${formatTime12Hour(bookingDate)} - ${formatTime12Hour(endTime)}</span>
                                </div>
                                <div class="flex justify-between mb-2">
                                    <span class="font-medium text-gray-700">Duration:</span>
                                    <span class="text-gray-900">${booking.duration_hours} hour${booking.duration_hours > 1 ? 's' : ''}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Refund Amount:</span>
                                    <span class="text-green-600 font-semibold">₹${booking.total_amount}</span>
                                </div>
                            </div>
                        `;

                        document.getElementById('cancelModal').classList.remove('hidden');
                    } else {
                        showErrorMessage('Error loading booking details. Please try again.');
                    }
                })
                .catch(error => {
                    showErrorMessage('Error loading booking details. Please try again.');
                });
        }

        function closeCancelModal() {
            document.getElementById('cancelModal').classList.add('hidden');
            currentCancelBookingId = null;
        }

        function confirmCancelBooking() {
            if (!currentCancelBookingId) return;

            const confirmBtn = document.getElementById('confirmCancelBtn');
            const btnText = document.getElementById('cancelBtnText');
            const btnLoading = document.getElementById('cancelBtnLoading');

            // Show loading state
            confirmBtn.disabled = true;
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');

            fetch(`/booking/${currentCancelBookingId}/cancel-pending`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    reason: 'Cancelled by client from notifications page'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showSuccessMessage(data.message);

                    // Close modal
                    closeCancelModal();

                    // Reload page to update notifications
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showErrorMessage(data.message || 'Error cancelling booking. Please try again.');
                }
            })
            .catch(error => {
                showErrorMessage('Error cancelling booking. Please try again.');
            })
            .finally(() => {
                // Reset button state
                confirmBtn.disabled = false;
                btnText.classList.remove('hidden');
                btnLoading.classList.add('hidden');
            });
        }

        // Redirect to Update Booking Function with enhanced validation
        function redirectToUpdateBooking(bookingId) {
            // Fetch booking details first to validate and get provider ID
            fetch(`/booking/${bookingId}/details`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const booking = data.booking;
                        const providerId = booking.provider_id;

                        // Check if booking can be updated (server-side validation)
                        if (!booking.can_update) {
                            const restrictions = booking.update_restrictions || [];
                            let errorMessage = 'This booking cannot be updated.';

                            if (restrictions.includes('Cannot update after meeting start time')) {
                                errorMessage = 'Cannot update booking after the meeting start time has passed. You can make a new booking instead.';
                            } else if (restrictions.includes('Accepted bookings cannot be updated')) {
                                errorMessage = 'Accepted bookings cannot be updated. You can make a new booking if needed.';
                            } else if (restrictions.includes('Rejected bookings cannot be updated')) {
                                errorMessage = 'Rejected bookings cannot be updated. You can make a new booking with different details.';
                            } else if (restrictions.includes('Cancelled bookings cannot be updated')) {
                                errorMessage = 'Cancelled bookings cannot be updated. You can make a new booking instead.';
                            } else if (restrictions.length > 0) {
                                errorMessage = restrictions.join('. ') + '.';
                            }

                            showErrorMessage(errorMessage);

                            // Optionally redirect to provider profile after showing error
                            setTimeout(() => {
                                window.location.href = `/find-person/${providerId}`;
                            }, 3000);
                            return;
                        }

                        // Create URL with booking ID for update
                        const updateUrl = `/find-person/${providerId}?update_booking=${bookingId}`;

                        // Redirect to provider page for update
                        window.location.href = updateUrl;
                    } else {
                        // Handle specific error cases
                        let errorMessage = 'Error loading booking details. Please try again.';

                        if (data.message) {
                            if (data.message.includes('not found')) {
                                errorMessage = 'Booking not found. It may have been cancelled or deleted.';
                            } else if (data.message.includes('Unauthorized') || data.message.includes('own bookings')) {
                                errorMessage = 'You can only update your own bookings.';
                            } else if (data.message.includes('Authentication')) {
                                errorMessage = 'Please log in to update your booking.';
                                // Redirect to login
                                setTimeout(() => {
                                    window.location.href = '/login?redirect=' + encodeURIComponent(window.location.href);
                                }, 2000);
                                return;
                            } else {
                                errorMessage = data.message;
                            }
                        }

                        showErrorMessage(errorMessage);
                    }
                })
                .catch(error => {
                    console.error('Error fetching booking details:', error);
                    showErrorMessage('Network error. Please check your connection and try again.');
                });
        }

        // Update Booking Modal Functions (keeping for backward compatibility)
        let currentUpdateBookingId = null;

        function showUpdateBookingModal(bookingId) {
            currentUpdateBookingId = bookingId;

            // Fetch booking details
            fetch(`/booking/${bookingId}/details`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const booking = data.booking;
                        const bookingDate = new Date(booking.booking_date);

                        // Set the provider's hourly rate for calculations
                        currentProviderHourlyRate = booking.provider ? booking.provider.hourly_rate : 500;

                        // Populate modal with current booking details
                        document.getElementById('updateBookingDate').value = booking.booking_date.split('T')[0];
                        document.getElementById('updateBookingTime').value = bookingDate.toTimeString().slice(0, 5);
                        document.getElementById('updateDurationHours').value = booking.duration_hours;
                        document.getElementById('updateLocation').value = booking.meeting_location;

                        // Show current booking info
                        document.getElementById('currentBookingInfo').innerHTML = `
                            <div class="text-sm text-gray-600 mb-4">
                                <h4 class="font-medium text-gray-800 mb-2">Current Booking Details:</h4>
                                <div class="bg-gray-50 p-3 rounded-lg">
                                    <div class="grid grid-cols-2 gap-2 text-xs">
                                        <div><span class="font-medium">Date:</span> ${formatDate(booking.booking_date)}</div>
                                        <div><span class="font-medium">Time:</span> ${formatTime12Hour(bookingDate)}</div>
                                        <div><span class="font-medium">Duration:</span> ${booking.duration_hours}h</div>
                                        <div><span class="font-medium">Amount:</span> ₹${booking.base_amount || booking.total_amount}</div>
                                    </div>
                                </div>
                            </div>
                        `;

                        // Calculate initial amount
                        calculateUpdateAmount();

                        document.getElementById('updateBookingModal').classList.remove('hidden');
                    } else {
                        showErrorMessage('Error loading booking details. Please try again.');
                    }
                })
                .catch(error => {
                    showErrorMessage('Error loading booking details. Please try again.');
                });
        }

        function closeUpdateBookingModal() {
            document.getElementById('updateBookingModal').classList.add('hidden');
            currentUpdateBookingId = null;
        }

        let currentProviderHourlyRate = 500; // Default rate, will be updated when booking details are fetched

        function calculateUpdateAmount() {
            const durationHours = parseFloat(document.getElementById('updateDurationHours').value) || 0;
            const newAmount = durationHours * currentProviderHourlyRate;

            document.getElementById('updateTotalAmount').textContent = newAmount.toFixed(0);
            document.getElementById('updateTotalHours').textContent = durationHours === 1 ? '1 hour' : `${durationHours} hours`;
        }

        function submitUpdateBooking() {
            if (!currentUpdateBookingId) return;

            const updateBtn = document.getElementById('updateBookingBtn');
            const btnText = document.getElementById('updateBtnText');
            const btnLoading = document.getElementById('updateBtnLoading');

            // Show loading state
            updateBtn.disabled = true;
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');

            const bookingDate = document.getElementById('updateBookingDate').value;
            const bookingTime = document.getElementById('updateBookingTime').value;
            const durationHours = document.getElementById('updateDurationHours').value;
            const location = document.getElementById('updateLocation').value;

            // Combine date and time
            const bookingDateTime = `${bookingDate}T${bookingTime}:00`;

            fetch(`/booking/${currentUpdateBookingId}/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    booking_date: bookingDateTime,
                    duration_hours: durationHours,
                    location: location
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showSuccessMessage(data.message);

                    // Close modal
                    closeUpdateBookingModal();

                    // Refresh page to show updated booking
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    if (data.errors) {
                        const errorMessages = Object.values(data.errors).flat().join('\n');
                        showErrorMessage('Validation errors:\n' + errorMessages);
                    } else {
                        showErrorMessage(data.message || 'Error updating booking. Please try again.');
                    }
                }
            })
            .catch(error => {
                console.error('Error updating booking:', error);
                showErrorMessage('Error updating booking. Please try again.');
            })
            .finally(() => {
                // Reset button state
                updateBtn.disabled = false;
                btnText.classList.remove('hidden');
                btnLoading.classList.add('hidden');
            });
        }

        // Add event listener for duration change
        document.addEventListener('DOMContentLoaded', function() {
            const durationInput = document.getElementById('updateDurationHours');
            if (durationInput) {
                durationInput.addEventListener('change', calculateUpdateAmount);
            }
        });

        // Helper functions for formatting
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                weekday: 'short',
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function formatTime12Hour(date) {
            if (typeof date === 'string') {
                date = new Date(date);
            }
            return date.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        }

        // Block client function
        function blockClient(bookingId) {
            if (confirm('Are you sure you want to block this client? They will not be able to book you again.')) {
                fetch(`/provider/booking/${bookingId}/block`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    // Handle subscription requirement error (403)
                    if (response.status === 403) {
                        return response.json().then(data => {
                            if (data.message && data.message.includes('subscription')) {
                                showErrorMessage('Active subscription required to block clients. Please update your subscription to continue.');
                                setTimeout(() => {
                                    window.location.href = '/profile?tab=time-spending';
                                }, 3000);
                                return { success: false, handled: true };
                            }
                            return data;
                        });
                    }

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return response.json();
                })
                .then(data => {
                    if (data.handled) {
                        return;
                    }

                    if (data.success) {
                        showSuccessMessage(data.message);
                        location.reload();
                    } else {
                        showErrorMessage(data.message || 'Error blocking client. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Block client error:', error);
                    showErrorMessage('Error blocking client. Please try again.');
                });
            }
        }

        // Unblock user function
        function unblockUser(userId) {
            if (confirm('Are you sure you want to unblock this user?')) {
                fetch(`/provider/user/${userId}/unblock`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    // Handle subscription requirement error (403)
                    if (response.status === 403) {
                        return response.json().then(data => {
                            if (data.message && data.message.includes('subscription')) {
                                showErrorMessage('Active subscription required to unblock users. Please update your subscription to continue.');
                                setTimeout(() => {
                                    window.location.href = '/profile?tab=time-spending';
                                }, 3000);
                                return { success: false, handled: true };
                            }
                            return data;
                        });
                    }

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return response.json();
                })
                .then(data => {
                    if (data.handled) {
                        return;
                    }

                    if (data.success) {
                        showSuccessMessage(data.message);
                        location.reload();
                    } else {
                        showErrorMessage(data.message || 'Error unblocking user. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Unblock user error:', error);
                    showErrorMessage('Error unblocking user. Please try again.');
                });
            }
        }

        // Open chat function
        function openChat(bookingId) {
            window.location.href = `/chat?booking_id=${bookingId}`;
        }

        // Show cancel modal
        function showCancelModal(bookingId) {
            currentActionBookingId = bookingId;

            // Fetch booking details to show in modal
            fetch(`/booking/${bookingId}/details`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const booking = data.booking;
                        const bookingDate = new Date(booking.booking_date);
                        const endTime = new Date(bookingDate.getTime() + (booking.duration_hours * 60 * 60 * 1000));

                        document.getElementById('cancelBookingDetails').innerHTML = `
                            <div class="text-sm space-y-1">
                                <div><strong>Provider:</strong> ${booking.provider.name}</div>
                                <div><strong>Date:</strong> ${bookingDate.toLocaleDateString()}</div>
                                <div><strong>Time:</strong> ${bookingDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})} - ${endTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                                <div><strong>Duration:</strong> ${booking.duration_hours} hour${booking.duration_hours > 1 ? 's' : ''}</div>
                                <div><strong>Amount:</strong> ₹${booking.total_amount}</div>
                            </div>
                        `;

                        document.getElementById('cancelModal').classList.remove('hidden');
                    } else {
                        showErrorMessage('Error loading booking details. Please try again.');
                    }
                })
                .catch(error => {
                    showErrorMessage('Error loading booking details. Please try again.');
                });
        }

        // Close cancel modal
        function closeCancelModal() {
            document.getElementById('cancelModal').classList.add('hidden');
            currentActionBookingId = null;
        }

        // Confirm cancel booking
        function confirmCancelBooking() {
            if (!currentActionBookingId) return;

            const confirmBtn = document.getElementById('confirmCancelBtn');
            const btnText = document.getElementById('cancelBtnText');
            const btnLoading = document.getElementById('cancelBtnLoading');

            // Show loading state
            confirmBtn.disabled = true;
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');

            fetch(`/booking/${currentActionBookingId}/cancel-pending`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessMessage(data.message);
                    closeCancelModal();
                    location.reload();
                } else {
                    showErrorMessage(data.message || 'Error cancelling booking. Please try again.');
                }
            })
            .catch(error => {
                showErrorMessage('Error cancelling booking. Please try again.');
            })
            .finally(() => {
                // Reset button state
                confirmBtn.disabled = false;
                btnText.classList.remove('hidden');
                btnLoading.classList.add('hidden');
            });
        }



        // Meeting Functions
        function startMeeting(bookingId) {
            openCameraModal('start', bookingId);
        }

        function endMeeting(bookingId) {
            openCameraModal('end', bookingId);
        }

        // Dynamic Meeting Button Functions
        async function checkPermissionsForMeeting() {
            try {
                const cameraGranted = await checkCameraPermission();
                const locationGranted = await checkLocationPermission();
                return cameraGranted && locationGranted;
            } catch (error) {
                console.error('Error checking permissions:', error);
                return false;
            }
        }

        async function updateMeetingButtonsOnPageLoad() {
            const startMeetingButtons = document.querySelectorAll('[id^="startMeetingBtn-"]');

            for (const button of startMeetingButtons) {
                // Check if this is already an "End Meeting" button (user has uploaded start photo)
                const buttonText = button.querySelector('.button-text');
                if (buttonText && buttonText.textContent.trim() === 'End Meeting') {
                    // Don't override End Meeting buttons - they're set correctly by server-side logic
                    continue;
                }

                // Only update Start Meeting buttons based on permissions
                const permissionsGranted = await checkPermissionsForMeeting();
                updateMeetingButtonState(button, permissionsGranted);
            }
        }

        function updateMeetingButtonState(button, permissionsGranted) {
            const buttonText = button.querySelector('.button-text');

            if (permissionsGranted) {
                buttonText.textContent = 'Start Meeting';
                button.className = 'px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors flex items-center';
                button.disabled = false;
            } else {
                buttonText.textContent = 'Grant Permissions to Start Meeting';
                button.className = 'px-4 py-2 text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 rounded-lg transition-colors flex items-center';
                button.disabled = false;
            }
        }

        async function handleStartMeetingClick(bookingId) {
            const button = document.getElementById(`startMeetingBtn-${bookingId}`);
            const buttonText = button.querySelector('.button-text');

            // Check current permission status
            const permissionsGranted = await checkPermissionsForMeeting();

            if (permissionsGranted) {
                // Permissions already granted - start camera directly
                startMeeting(bookingId);
            } else {
                // Need to request permissions
                buttonText.textContent = 'Requesting Permissions...';
                button.disabled = true;
                button.className = 'px-4 py-2 text-sm font-medium text-white bg-gray-500 rounded-lg transition-colors flex items-center cursor-not-allowed';

                try {
                    // Request camera permission first
                    const cameraGranted = await requestCameraPermissionForMeeting();

                    if (cameraGranted) {
                        // Request location permission
                        const locationGranted = await requestLocationPermissionForMeeting();

                        if (locationGranted) {
                            // Both permissions granted - update button and start meeting
                            updateMeetingButtonState(button, true);
                            setTimeout(() => startMeeting(bookingId), 500);
                        } else {
                            // Location permission denied
                            buttonText.textContent = 'Location Permission Required';
                            button.className = 'px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center';
                            button.disabled = false;
                        }
                    } else {
                        // Camera permission denied
                        buttonText.textContent = 'Camera Permission Required';
                        button.className = 'px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center';
                        button.disabled = false;
                    }
                } catch (error) {
                    console.error('Error requesting permissions:', error);
                    buttonText.textContent = 'Permission Error - Try Again';
                    button.className = 'px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center';
                    button.disabled = false;
                }
            }
        }

        async function requestCameraPermissionForMeeting() {
            try {
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    return false;
                }

                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'user',
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                });

                // Stop the stream immediately as we're just checking permission
                stream.getTracks().forEach(track => track.stop());
                return true;
            } catch (error) {
                console.error('Camera permission request failed:', error);
                return false;
            }
        }

        async function requestLocationPermissionForMeeting() {
            return new Promise((resolve) => {
                if (!navigator.geolocation) {
                    resolve(false);
                    return;
                }

                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        resolve(true);
                    },
                    (error) => {
                        console.error('Location permission request failed:', error);
                        resolve(false);
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 0
                    }
                );
            });
        }

        function updateMeetingCardAfterStartPhoto(bookingId, photoBlob) {
            try {
                // For now, just reload the page to show the updated photos in the new grid layout
                // This ensures the server-side logic properly displays all photos
                location.reload();

                // TODO: Implement dynamic photo grid update if needed
                // The new grid layout makes it complex to update dynamically,
                // so page reload ensures consistency with server-side rendering
            } catch (error) {
                console.error('Error updating meeting card after photo upload:', error);
                location.reload();
            }
        }

        // Meeting Verification Camera Functions
        let currentStream = null;
        let currentPhotoType = null; // 'start' or 'end'
        let currentCameraBookingId = null;
        let capturedPhotoBlob = null;
        let currentLocation = null;

        // Location accuracy validation
        function validateLocationAccuracy(accuracy) {
            if (accuracy <= 10) {
                return { status: 'excellent', message: 'Excellent location accuracy', color: 'green' };
            } else if (accuracy <= 50) {
                return { status: 'good', message: 'Good location accuracy', color: 'green' };
            } else if (accuracy <= 100) {
                return { status: 'fair', message: 'Fair location accuracy', color: 'yellow' };
            } else {
                return { status: 'poor', message: 'Poor location accuracy - consider moving to an open area', color: 'red' };
            }
        }

        // Continuous location monitoring for better accuracy
        let locationWatchId = null;

        function startLocationMonitoring() {
            if (navigator.geolocation && !locationWatchId) {
                locationWatchId = navigator.geolocation.watchPosition(
                    function(position) {
                        // Update location if we get better accuracy
                        if (!currentLocation || position.coords.accuracy < currentLocation.accuracy) {
                            currentLocation = {
                                latitude: position.coords.latitude,
                                longitude: position.coords.longitude,
                                accuracy: position.coords.accuracy,
                                altitude: position.coords.altitude,
                                altitudeAccuracy: position.coords.altitudeAccuracy,
                                heading: position.coords.heading,
                                speed: position.coords.speed,
                                timestamp: position.timestamp
                            };

                            console.log('Updated location with better accuracy:', position.coords.accuracy, 'meters');

                            // Update the display if location status is visible
                            const locationStatus = document.getElementById('locationStatus');
                            if (locationStatus && !locationStatus.classList.contains('hidden')) {
                                getAddressFromCoordinates(position.coords.latitude, position.coords.longitude);
                            }
                        }
                    },
                    function(error) {
                        console.log('Location monitoring error:', error);
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 0
                    }
                );
            }
        }

        function stopLocationMonitoring() {
            if (locationWatchId) {
                navigator.geolocation.clearWatch(locationWatchId);
                locationWatchId = null;
            }
        }

        function ensureCameraElements() {
            // Prevent multiple calls
            if (window.cameraElementsEnsured) {
                console.log('Camera elements already ensured, skipping...');
                return;
            }

            console.log('Ensuring camera elements exist...');

            // First ensure containers exist
            let cameraContainer = document.getElementById('cameraContainer');
            if (!cameraContainer) {
                console.log('Creating cameraContainer...');
                cameraContainer = document.createElement('div');
                cameraContainer.id = 'cameraContainer';
                cameraContainer.className = 'mb-4';

                const modal = document.getElementById('cameraModal');
                if (modal) {
                    const modalBody = modal.querySelector('.p-6') || modal.querySelector('.relative');
                    if (modalBody) {
                        modalBody.appendChild(cameraContainer);
                    }
                }
            }

            let photoPreview = document.getElementById('photoPreview');
            if (!photoPreview) {
                console.log('Creating photoPreview...');
                photoPreview = document.createElement('div');
                photoPreview.id = 'photoPreview';
                photoPreview.className = 'mb-4 hidden';

                const modal = document.getElementById('cameraModal');
                if (modal) {
                    const modalBody = modal.querySelector('.p-6') || modal.querySelector('.relative');
                    if (modalBody) {
                        modalBody.appendChild(photoPreview);
                    }
                }
            }

            // Now ensure required elements exist
            const requiredElements = [
                {
                    id: 'cameraPreview',
                    tag: 'video',
                    attributes: {
                        autoplay: 'true',
                        playsinline: 'true',
                        class: 'w-full h-64 bg-gray-200 rounded-lg object-cover'
                    },
                    container: cameraContainer
                },
                {
                    id: 'photoCanvas',
                    tag: 'canvas',
                    attributes: {
                        class: 'hidden'
                    },
                    container: cameraContainer
                },
                {
                    id: 'capturedPhoto',
                    tag: 'img',
                    attributes: {
                        alt: 'Captured photo',
                        class: 'w-full h-64 bg-gray-200 rounded-lg object-cover'
                    },
                    container: photoPreview
                }
            ];

            requiredElements.forEach(({ id, tag, attributes, container }) => {
                let element = document.getElementById(id);
                if (!element && container) {
                    console.log(`Creating missing element: ${id}`);
                    element = document.createElement(tag);
                    element.id = id;

                    // Set attributes
                    Object.entries(attributes).forEach(([key, value]) => {
                        element.setAttribute(key, value);
                    });

                    container.appendChild(element);
                    console.log(`✅ Created ${id} element`);
                } else if (element) {
                    console.log(`✅ Found existing ${id} element`);
                } else {
                    console.log(`❌ Could not create ${id} - container missing`);
                }
            });

            console.log('Camera elements check complete');

            // Mark as ensured to prevent multiple calls
            window.cameraElementsEnsured = true;
        }

        async function openCameraModal(photoType, bookingId) {
            currentPhotoType = photoType;
            currentCameraBookingId = bookingId;
            currentLocation = null;

            const modal = document.getElementById('cameraModal');
            const title = document.getElementById('cameraModalTitle');

            if (title) {
                title.textContent = photoType === 'start' ? 'Take Meeting Start Photo Together' : 'Take Meeting End Photo Together';
            }

            // Ensure all camera elements exist before proceeding
            ensureCameraElements();

            // Reset modal state
            resetCameraModal();

            // Show modal first
            if (modal) {
                modal.classList.remove('hidden');
            }

            // Start location monitoring for continuous accuracy improvement
            startLocationMonitoring();

            // Automatically start camera when modal opens
            try {
                // Check camera permission first
                const cameraPermissionGranted = await checkCameraPermission();

                if (cameraPermissionGranted) {
                    // Request permissions and start camera automatically
                    const permissionsGranted = await checkAndRequestMandatoryPermissions();
                    if (permissionsGranted) {
                        await startCamera();
                    } else {
                        // If permissions not granted, show permission request UI
                        showPermissionRequestUI();
                    }
                } else {
                    // Camera permission not granted, show permission request UI
                    showPermissionRequestUI();
                }
            } catch (error) {
                console.error('Error starting camera automatically:', error);
                // Fallback to showing permission request UI and start camera button
                showPermissionRequestUI();
                const startCameraBtn = document.getElementById('startCameraBtn');
                if (startCameraBtn) {
                    startCameraBtn.classList.remove('hidden');
                }
            }
        }

        function showPermissionRequestUI() {
            const permissionDiv = document.getElementById('cameraPermissionRequired');
            const startCameraBtn = document.getElementById('startCameraBtn');
            const locationStatus = document.getElementById('locationStatus');

            if (permissionDiv) {
                permissionDiv.classList.remove('hidden');
            }
            if (startCameraBtn) {
                startCameraBtn.classList.add('hidden');
            }
            if (locationStatus) {
                locationStatus.classList.add('hidden');
            }
        }

        function closeCameraModal() {
            // Stop camera stream
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
            }

            // Stop location monitoring
            stopLocationMonitoring();

            // Reset variables
            currentPhotoType = null;
            currentCameraBookingId = null;
            capturedPhotoBlob = null;
            currentLocation = null;

            // Hide modal
            document.getElementById('cameraModal').classList.add('hidden');

            // Reset modal state
            resetCameraModal();
        }

        function resetCameraModal() {
            try {
                // Hide all error and permission states
                const cameraError = document.getElementById('cameraError');
                const cameraPermissionRequired = document.getElementById('cameraPermissionRequired');

                if (cameraError) cameraError.classList.add('hidden');
                if (cameraPermissionRequired) cameraPermissionRequired.classList.add('hidden');

                // Show camera container, hide photo preview
                const cameraContainer = document.getElementById('cameraContainer');
                const photoPreview = document.getElementById('photoPreview');

                if (cameraContainer) cameraContainer.classList.remove('hidden');
                if (photoPreview) photoPreview.classList.add('hidden');

                // Reset buttons - show start camera button initially
                const startCameraBtn = document.getElementById('startCameraBtn');
                const captureBtn = document.getElementById('captureBtn');
                const retakeBtn = document.getElementById('retakeBtn');
                const uploadBtn = document.getElementById('uploadBtn');

                if (startCameraBtn) startCameraBtn.classList.remove('hidden');
                if (captureBtn) captureBtn.classList.add('hidden');
                if (retakeBtn) retakeBtn.classList.add('hidden');
                if (uploadBtn) uploadBtn.classList.add('hidden');

                // Reset upload button state
                const uploadBtnText = document.getElementById('uploadBtnText');
                const uploadBtnLoading = document.getElementById('uploadBtnLoading');

                if (uploadBtn) {
                    uploadBtn.disabled = false;
                }
                if (uploadBtnText) uploadBtnText.classList.remove('hidden');
                if (uploadBtnLoading) uploadBtnLoading.classList.add('hidden');

                // Reset permission button state
                const requestBtn = document.getElementById('requestCameraPermissionBtn');
                if (requestBtn) {
                    requestBtn.disabled = false;
                    requestBtn.textContent = 'Grant Camera Permission';
                }

                // Clear any captured photo data
                capturedPhotoBlob = null;

                // Clear photo preview
                const capturedImg = document.getElementById('capturedPhoto');
                if (capturedImg && capturedImg.src) {
                    URL.revokeObjectURL(capturedImg.src);
                    capturedImg.src = '';
                }

            } catch (error) {
                console.error('Reset camera modal failed:', error);
                // Don't show error to user as this is a reset function
            }
        }

        function showCameraError(message) {
            const errorDiv = document.getElementById('cameraError');
            const errorText = document.getElementById('cameraErrorText');

            if (errorText) {
                errorText.textContent = message;
            }
            if (errorDiv) {
                errorDiv.classList.remove('hidden');
            }
        }

        // Check and request mandatory permissions (camera and location)
        async function checkAndRequestMandatoryPermissions() {
            const locationText = document.getElementById('locationText');
            const locationStatus = document.getElementById('locationStatus');

            try {
                // Update status to show checking permissions
                locationText.textContent = 'Checking location permissions...';
                locationStatus.className = 'mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg';
                locationStatus.classList.remove('hidden');

                // Camera permission should already be checked before calling this function
                // Skip camera check here to avoid redundant checks

                // Check location permission
                const locationPermissionGranted = await checkLocationPermission();
                if (!locationPermissionGranted) {
                    locationText.textContent = 'Location access is required for meeting verification. Please allow location access and try again.';
                    locationStatus.className = 'mb-4 p-3 bg-red-50 border border-red-200 rounded-lg';
                    showCameraError('Location access is required for meeting verification. Please allow location access in your browser settings and refresh the page.');
                    return false;
                }

                // Both permissions granted
                locationText.textContent = 'Permissions granted. Getting precise location...';
                locationStatus.className = 'mb-4 p-3 bg-green-50 border border-green-200 rounded-lg';

                // Get current location
                await getCurrentLocationForMeeting();
                return true;

            } catch (error) {
                console.error('Permission check error:', error);
                locationText.textContent = 'Error checking permissions. Please refresh and try again.';
                locationStatus.className = 'mb-4 p-3 bg-red-50 border border-red-200 rounded-lg';
                showCameraError('Error checking permissions. Please refresh the page and try again.');
                return false;
            }
        }

        // Check camera permission
        async function checkCameraPermission() {
            try {
                // Check if getUserMedia is supported
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    return false;
                }

                // Check permission status first if available
                if (navigator.permissions && navigator.permissions.query) {
                    try {
                        const permission = await navigator.permissions.query({ name: 'camera' });
                        if (permission.state === 'denied') {
                            return false;
                        }
                        if (permission.state === 'granted') {
                            // Permission is granted, return true without testing camera access
                            // to avoid unnecessary camera activation
                            return true;
                        }
                        // If 'prompt', return false - permission not yet granted
                        if (permission.state === 'prompt') {
                            return false;
                        }
                    } catch (e) {
                        // Permission API not available, fall through to getUserMedia test
                        console.log('Permission API not available, testing camera access directly');
                    }
                }

                // Fallback: Try to get camera access without prompting
                // This should only be reached if Permission API is not available
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({
                        video: {
                            facingMode: 'user',
                            width: { ideal: 640 },
                            height: { ideal: 480 }
                        }
                    });

                    // Stop the stream immediately as we're just checking permission
                    stream.getTracks().forEach(track => track.stop());
                    return true;
                } catch (error) {
                    // If this fails, permission is likely not granted
                    return false;
                }

            } catch (error) {
                console.error('Camera permission check failed:', error.name, error.message);
                return false;
            }
        }

        // Request camera permission explicitly
        async function requestCameraPermission() {
            const permissionDiv = document.getElementById('cameraPermissionRequired');
            const errorDiv = document.getElementById('cameraError');
            const errorText = document.getElementById('cameraErrorText');
            const requestBtn = document.getElementById('requestCameraPermissionBtn');

            try {
                // Update button state
                if (requestBtn) {
                    requestBtn.disabled = true;
                    requestBtn.textContent = 'Requesting Permission...';
                }

                // Check if getUserMedia is supported
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('Camera access is not supported in this browser.');
                }

                // Request camera access - this will trigger the browser permission popup
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'user',
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                });

                // Stop the stream immediately
                stream.getTracks().forEach(track => track.stop());

                // Hide permission request and error divs
                if (permissionDiv) {
                    permissionDiv.classList.add('hidden');
                }
                if (errorDiv) {
                    errorDiv.classList.add('hidden');
                }

                // Automatically start camera after permission granted
                await startCamera();

            } catch (error) {
                // Re-enable button
                if (requestBtn) {
                    requestBtn.disabled = false;
                    requestBtn.textContent = 'Grant Camera Permission';
                }

                // Show error message
                let errorMessage = 'Failed to access camera. ';

                if (error.name === 'NotAllowedError') {
                    errorMessage += 'Please click "Allow" when your browser asks for camera permission. If you accidentally clicked "Block", please:';
                    errorMessage += `
                        <br><br><strong>To enable camera permission:</strong>
                        <ol style="margin: 10px 0; padding-left: 20px;">
                            <li>Click the camera icon in your browser's address bar</li>
                            <li>Select "Always allow" for camera access</li>
                            <li>Refresh this page and try again</li>
                        </ol>
                        <small>Or go to your browser settings and allow camera access for this site.</small>
                    `;
                } else if (error.name === 'NotFoundError') {
                    errorMessage += 'No camera found on this device. Please ensure you have a working camera connected.';
                } else if (error.name === 'NotSupportedError') {
                    errorMessage += 'Camera access is not supported in this browser.';
                } else {
                    errorMessage += error.message || 'Unknown error occurred.';
                }

                if (errorText) {
                    errorText.innerHTML = errorMessage;
                }
                if (errorDiv) {
                    errorDiv.classList.remove('hidden');
                }
            }
        }

        // Request location permission explicitly
        async function requestLocationPermission() {
            const requestBtn = document.getElementById('requestLocationPermissionBtn');
            const errorDiv = document.getElementById('cameraError');
            const errorText = document.getElementById('cameraErrorText');

            try {
                // Update button state
                if (requestBtn) {
                    requestBtn.disabled = true;
                    requestBtn.textContent = '📍 Requesting Location Permission...';
                }

                // Check if geolocation is supported
                if (!navigator.geolocation) {
                    throw new Error('Geolocation is not supported in this browser.');
                }

                // Request location access - this will trigger the browser permission popup
                await new Promise((resolve, reject) => {
                    navigator.geolocation.getCurrentPosition(
                        function(position) {
                            // Store the location
                            currentLocation = {
                                latitude: position.coords.latitude,
                                longitude: position.coords.longitude,
                                accuracy: position.coords.accuracy,
                                altitude: position.coords.altitude,
                                altitudeAccuracy: position.coords.altitudeAccuracy,
                                heading: position.coords.heading,
                                speed: position.coords.speed,
                                timestamp: position.timestamp
                            };

                            console.log('Location permission granted, accuracy:', position.coords.accuracy, 'meters');
                            resolve(position);
                        },
                        function(error) {
                            reject(error);
                        },
                        {
                            enableHighAccuracy: true,
                            timeout: 15000,
                            maximumAge: 0
                        }
                    );
                });

                // Success - update button
                if (requestBtn) {
                    requestBtn.textContent = '📍 Location Permission Granted!';
                    requestBtn.style.backgroundColor = '#10b981';
                }

                // Hide error if it was showing
                if (errorDiv) {
                    errorDiv.classList.add('hidden');
                }

                // Refresh meeting button states since location permission is now granted
                updateMeetingButtonsOnPageLoad();

                // Re-enable after a moment
                setTimeout(() => {
                    if (requestBtn) {
                        requestBtn.disabled = false;
                        requestBtn.textContent = '📍 Grant Location Permission';
                        requestBtn.style.backgroundColor = '';
                    }
                }, 2000);

            } catch (error) {
                // Re-enable button
                if (requestBtn) {
                    requestBtn.disabled = false;
                    requestBtn.textContent = '📍 Grant Location Permission';
                    requestBtn.style.backgroundColor = '';
                }

                // Show error message
                let errorMessage = 'Failed to access location. ';

                if (error.code === error.PERMISSION_DENIED) {
                    errorMessage += 'Please click "Allow" when your browser asks for location permission. If you accidentally clicked "Block", please:';
                    errorMessage += `
                        <br><br><strong>To enable location permission:</strong>
                        <ol style="margin: 10px 0; padding-left: 20px;">
                            <li>Click the location icon in your browser's address bar</li>
                            <li>Select "Always allow" for location access</li>
                            <li>Refresh this page and try again</li>
                        </ol>
                        <small>Or go to your browser settings and allow location access for this site.</small>
                    `;
                } else if (error.code === error.POSITION_UNAVAILABLE) {
                    errorMessage += 'Location information is unavailable. Please ensure location services are enabled on your device.';
                } else if (error.code === error.TIMEOUT) {
                    errorMessage += 'Location request timed out. Please try again.';
                } else {
                    errorMessage += error.message || 'Unknown error occurred.';
                }

                if (errorText) {
                    errorText.innerHTML = errorMessage;
                }
                if (errorDiv) {
                    errorDiv.classList.remove('hidden');
                }
            }
        }

        // Check location permission
        async function checkLocationPermission() {
            try {
                if (!navigator.geolocation) {
                    return false;
                }

                // Check permission status first if available
                if (navigator.permissions && navigator.permissions.query) {
                    try {
                        const permission = await navigator.permissions.query({ name: 'geolocation' });
                        if (permission.state === 'denied') {
                            return false;
                        }
                        if (permission.state === 'granted') {
                            // Permission is granted, return true without requesting location
                            return true;
                        }
                        // If 'prompt', return false - permission not yet granted
                        if (permission.state === 'prompt') {
                            return false;
                        }
                    } catch (e) {
                        // Permission API not available, fall through to geolocation test
                        console.log('Permission API not available for geolocation, testing access directly');
                    }
                }

                // Fallback: Try to get location with a very short timeout to avoid prompting
                return new Promise((resolve) => {
                    navigator.geolocation.getCurrentPosition(
                        function(position) {
                            // Permission granted, store location for later use
                            currentLocation = {
                                latitude: position.coords.latitude,
                                longitude: position.coords.longitude,
                                accuracy: position.coords.accuracy,
                                altitude: position.coords.altitude,
                                altitudeAccuracy: position.coords.altitudeAccuracy,
                                heading: position.coords.heading,
                                speed: position.coords.speed,
                                timestamp: position.timestamp
                            };
                            resolve(true);
                        },
                        function(error) {
                            // Permission denied or not available
                            resolve(false);
                        },
                        {
                            enableHighAccuracy: false,
                            timeout: 1000, // Very short timeout to avoid long waits
                            maximumAge: 300000 // Accept cached location up to 5 minutes old
                        }
                    );
                });

            } catch (error) {
                console.error('Location permission check failed:', error);
                return false;
            }
        }

        // Get current location for meeting verification (updated to be async)
        async function getCurrentLocationForMeeting() {
            const locationText = document.getElementById('locationText');
            const locationStatus = document.getElementById('locationStatus');

            return new Promise((resolve) => {
                if (currentLocation) {
                    // Location already obtained during permission check
                    getAddressFromCoordinates(currentLocation.latitude, currentLocation.longitude);
                    resolve(true);
                    return;
                }

                locationText.textContent = 'Getting precise location...';
                locationStatus.className = 'mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg';

                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        function(position) {
                            currentLocation = {
                                latitude: position.coords.latitude,
                                longitude: position.coords.longitude,
                                accuracy: position.coords.accuracy,
                                altitude: position.coords.altitude,
                                altitudeAccuracy: position.coords.altitudeAccuracy,
                                heading: position.coords.heading,
                                speed: position.coords.speed,
                                timestamp: position.timestamp
                            };

                            console.log('Location accuracy:', position.coords.accuracy, 'meters');

                            // Get address from coordinates
                            getAddressFromCoordinates(position.coords.latitude, position.coords.longitude);
                            resolve(true);
                        },
                        function(error) {
                            console.error('High accuracy location error:', error);

                            // Try again with lower accuracy as fallback
                            navigator.geolocation.getCurrentPosition(
                                function(position) {
                                    currentLocation = {
                                        latitude: position.coords.latitude,
                                        longitude: position.coords.longitude,
                                        accuracy: position.coords.accuracy,
                                        altitude: position.coords.altitude,
                                        altitudeAccuracy: position.coords.altitudeAccuracy,
                                        heading: position.coords.heading,
                                        speed: position.coords.speed,
                                        timestamp: position.timestamp
                                    };

                                    console.log('Fallback location accuracy:', position.coords.accuracy, 'meters');

                                    // Warn if accuracy is poor
                                    if (position.coords.accuracy > 100) {
                                        locationText.textContent = `Location obtained with low accuracy (±${Math.round(position.coords.accuracy)}m). For better accuracy, please enable high accuracy location services.`;
                                        locationStatus.className = 'mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg';
                                    }

                                    getAddressFromCoordinates(position.coords.latitude, position.coords.longitude);
                                    resolve(true);
                                },
                                function(fallbackError) {
                                    console.error('Fallback location error:', fallbackError);
                                    locationText.textContent = 'Location access is required for meeting verification. Please enable location services and allow high accuracy.';
                                    locationStatus.className = 'mb-4 p-3 bg-red-50 border border-red-200 rounded-lg';
                                    resolve(false);
                                },
                                {
                                    enableHighAccuracy: false,
                                    timeout: 10000,
                                    maximumAge: 300000 // 5 minutes for fallback
                                }
                            );
                        },
                        {
                            enableHighAccuracy: true,
                            timeout: 15000, // Increased timeout for better accuracy
                            maximumAge: 0 // Force fresh location reading for 100% accuracy
                        }
                    );
                } else {
                    locationText.textContent = 'Geolocation is not supported by this browser.';
                    locationStatus.className = 'mb-4 p-3 bg-red-50 border border-red-200 rounded-lg';
                    resolve(false);
                }
            });
        }

        // Get address from coordinates using reverse geocoding
        async function getAddressFromCoordinates(lat, lng) {
            const locationText = document.getElementById('locationText');
            const locationStatus = document.getElementById('locationStatus');

            try {
                // Try BigDataCloud API first
                const response = await fetch(`https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`);

                if (!response.ok) {
                    throw new Error('BigDataCloud API failed');
                }

                const data = await response.json();
                const address = data.locality || data.city || data.principalSubdivision || 'Unknown location';

                if (currentLocation && currentLocation.accuracy) {
                    const validation = validateLocationAccuracy(currentLocation.accuracy);
                    const accuracyText = ` (±${Math.round(currentLocation.accuracy)}m - ${validation.message})`;
                    locationText.textContent = `Location: ${address}${accuracyText}`;

                    // Set status color based on accuracy
                    if (validation.color === 'green') {
                        locationStatus.className = 'mb-4 p-3 bg-green-50 border border-green-200 rounded-lg';
                    } else if (validation.color === 'yellow') {
                        locationStatus.className = 'mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg';
                    } else {
                        locationStatus.className = 'mb-4 p-3 bg-red-50 border border-red-200 rounded-lg';
                    }
                } else {
                    locationText.textContent = `Location: ${address}`;
                    locationStatus.className = 'mb-4 p-3 bg-green-50 border border-green-200 rounded-lg';
                }

                // Store full address in location object
                if (currentLocation) {
                    currentLocation.address = address;
                    currentLocation.fullAddress = data.locality ?
                        `${data.locality}, ${data.principalSubdivision}, ${data.countryName}` :
                        address;
                }
            } catch (error) {
                // Handle error silently

                try {
                    // Fallback to OpenStreetMap Nominatim
                    const fallbackResponse = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=10&addressdetails=1`);

                    if (!fallbackResponse.ok) {
                        throw new Error('Nominatim API failed');
                    }

                    const fallbackData = await fallbackResponse.json();
                    const address = fallbackData.address?.city || fallbackData.address?.town || fallbackData.address?.village || fallbackData.address?.state || 'Unknown location';
                    locationText.textContent = `Location: ${address}`;
                    locationStatus.className = 'mb-4 p-3 bg-green-50 border border-green-200 rounded-lg';

                    // Store address data
                    if (currentLocation) {
                        currentLocation.address = address;
                        currentLocation.fullAddress = fallbackData.display_name || `${address}`;
                    }
                } catch (fallbackError) {
                    console.error('Error getting address from Nominatim:', fallbackError);
                    // Use coordinates as final fallback
                    locationText.textContent = `Location: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;
                    locationStatus.className = 'mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg';

                    // Store coordinates as fallback
                    if (currentLocation) {
                        currentLocation.address = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
                        currentLocation.fullAddress = `Coordinates: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;
                    }
                }
            }
        }

        async function startCamera() {
            try {


                // Check if getUserMedia is supported
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('Camera access is not supported in this browser.');
                }

                // Request camera access
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'user',
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                });


                currentStream = stream;
                const video = document.getElementById('cameraPreview');
                video.srcObject = stream;

                // Update button visibility
                document.getElementById('startCameraBtn').classList.add('hidden');
                document.getElementById('captureBtn').classList.remove('hidden');

                // Hide permission request and error divs
                document.getElementById('cameraPermissionRequired').classList.add('hidden');
                document.getElementById('cameraError').classList.add('hidden');

                // Show location status again if it was hidden
                document.getElementById('locationStatus').classList.remove('hidden');

                // Refresh meeting button states since camera permission is now granted
                updateMeetingButtonsOnPageLoad();

            } catch (error) {
                console.error('Camera start failed:', error);

                let errorMessage = 'Unable to access camera. ';

                if (error.name === 'NotAllowedError') {
                    // Permission denied - show permission request UI
                    document.getElementById('cameraPermissionRequired').classList.remove('hidden');
                    document.getElementById('startCameraBtn').classList.add('hidden');
                    document.getElementById('locationStatus').classList.add('hidden');
                    return;
                } else if (error.name === 'NotFoundError') {
                    errorMessage += 'No camera found on this device.';
                } else if (error.name === 'NotSupportedError') {
                    errorMessage += 'Camera access is not supported in this browser.';
                } else if (error.name === 'NotReadableError') {
                    errorMessage += 'Camera is already in use by another application.';
                } else {
                    errorMessage += 'Please ensure you have granted camera permissions and try again.';
                }

                showCameraError(errorMessage);
                // Show start camera button again for retry
                document.getElementById('startCameraBtn').classList.remove('hidden');
            }
        }

        function capturePhoto() {
            try {
                // Debug: Check if modal is visible
                const modal = document.getElementById('cameraModal');
                if (!modal || modal.classList.contains('hidden')) {
                    throw new Error('Camera modal is not open. Please open the camera modal first.');
                }

                const video = document.getElementById('cameraPreview');
                let canvas = document.getElementById('photoCanvas');

                // Debug: Log element states
                console.log('Debug - Elements check:', {
                    modal: modal ? 'found' : 'not found',
                    modalHidden: modal ? modal.classList.contains('hidden') : 'unknown',
                    video: video ? 'found' : 'not found',
                    canvas: canvas ? 'found' : 'not found'
                });

                // Validate required elements exist
                if (!video) {
                    throw new Error('Camera preview video element not found');
                }

                if (!canvas) {
                    // Try to create canvas dynamically if it doesn't exist
                    console.log('Canvas not found, creating dynamically...');

                    // Check if canvas already exists but with different ID or was just created
                    const existingCanvas = document.querySelector('canvas[id="photoCanvas"]');
                    if (existingCanvas) {
                        canvas = existingCanvas;
                        console.log('Found existing canvas with correct ID');
                    } else {
                        const newCanvas = document.createElement('canvas');
                        newCanvas.id = 'photoCanvas';
                        newCanvas.className = 'hidden';

                        // Add to camera container
                        const cameraContainer = document.getElementById('cameraContainer');
                        if (cameraContainer) {
                            cameraContainer.appendChild(newCanvas);
                            canvas = newCanvas;
                            console.log('Canvas created and added to DOM');
                        } else {
                            throw new Error('Photo canvas element not found and camera container not available to create one');
                        }
                    }
                }

                // Check if video is ready
                if (!video.videoWidth || !video.videoHeight) {
                    throw new Error('Camera video is not ready. Please wait for the camera to load.');
                }

                const context = canvas.getContext('2d');
                if (!context) {
                    throw new Error('Unable to get canvas 2D context');
                }

                // Set canvas dimensions to match video
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;

                // Draw video frame to canvas
                context.drawImage(video, 0, 0, canvas.width, canvas.height);

                // Convert canvas to blob
                canvas.toBlob(function(blob) {
                    if (!blob) {
                        showCameraError('Failed to capture photo. Please try again.');
                        return;
                    }

                    capturedPhotoBlob = blob;

                    // Show captured photo
                    const capturedImg = document.getElementById('capturedPhoto');
                    if (capturedImg) {
                        capturedImg.src = URL.createObjectURL(blob);
                    }

                    // Hide camera, show photo preview
                    const cameraContainer = document.getElementById('cameraContainer');
                    const photoPreview = document.getElementById('photoPreview');

                    if (cameraContainer) cameraContainer.classList.add('hidden');
                    if (photoPreview) photoPreview.classList.remove('hidden');

                    // Update button visibility
                    const captureBtn = document.getElementById('captureBtn');
                    const retakeBtn = document.getElementById('retakeBtn');
                    const uploadBtn = document.getElementById('uploadBtn');

                    if (captureBtn) captureBtn.classList.add('hidden');
                    if (retakeBtn) retakeBtn.classList.remove('hidden');
                    if (uploadBtn) uploadBtn.classList.remove('hidden');

                    // Stop camera stream
                    if (currentStream) {
                        currentStream.getTracks().forEach(track => track.stop());
                        currentStream = null;
                    }
                }, 'image/jpeg', 0.8);

            } catch (error) {
                console.error('Photo capture failed:', error);
                showCameraError(error.message || 'Failed to capture photo. Please try again.');
            }
        }

        async function retakePhoto() {
            try {
                // Reset to camera view
                const cameraContainer = document.getElementById('cameraContainer');
                const photoPreview = document.getElementById('photoPreview');

                if (cameraContainer) cameraContainer.classList.remove('hidden');
                if (photoPreview) photoPreview.classList.add('hidden');

                // Reset buttons - hide all initially
                const startCameraBtn = document.getElementById('startCameraBtn');
                const captureBtn = document.getElementById('captureBtn');
                const retakeBtn = document.getElementById('retakeBtn');
                const uploadBtn = document.getElementById('uploadBtn');

                if (startCameraBtn) startCameraBtn.classList.add('hidden');
                if (captureBtn) captureBtn.classList.add('hidden');
                if (retakeBtn) retakeBtn.classList.add('hidden');
                if (uploadBtn) uploadBtn.classList.add('hidden');

                // Clear captured photo
                capturedPhotoBlob = null;

                // Clear any existing photo preview
                const capturedImg = document.getElementById('capturedPhoto');
                if (capturedImg && capturedImg.src) {
                    URL.revokeObjectURL(capturedImg.src);
                    capturedImg.src = '';
                }

                // Restart camera automatically
                await startCamera();

            } catch (error) {
                console.error('Retake photo failed:', error);
                showCameraError('Failed to restart camera. Please try again.');
            }
        }

        async function uploadPhoto() {
            if (!capturedPhotoBlob || !currentPhotoType || !currentCameraBookingId) {
                showCameraError('Missing photo or booking information. Please try again.');
                return;
            }

            // Check if location is available (now mandatory)
            if (!currentLocation || !currentLocation.latitude || !currentLocation.longitude) {
                showCameraError('Location is required for meeting verification. Please ensure location access is enabled and try again.');
                return;
            }

            // Confirm that both participants are in the photo
            const confirmMessage = `Please confirm that both you and your meeting partner are clearly visible together in this photo.\n\nThis photo is required for meeting verification and should show both participants in the same image.`;
            if (!confirm(confirmMessage)) {
                return;
            }

            const uploadBtn = document.getElementById('uploadBtn');
            const uploadBtnText = document.getElementById('uploadBtnText');
            const uploadBtnLoading = document.getElementById('uploadBtnLoading');

            // Show loading state
            uploadBtn.disabled = true;
            uploadBtnText.classList.add('hidden');
            uploadBtnLoading.classList.remove('hidden');

            try {
                const formData = new FormData();
                formData.append('photo', capturedPhotoBlob, `meeting_${currentPhotoType}_photo.jpg`);

                // Add location data (now mandatory)
                formData.append('latitude', currentLocation.latitude);
                formData.append('longitude', currentLocation.longitude);
                formData.append('accuracy', currentLocation.accuracy || 0);
                formData.append('address', currentLocation.address || '');
                formData.append('full_address', currentLocation.fullAddress || '');

                const endpoint = currentPhotoType === 'start'
                    ? `/meeting-verification/${currentCameraBookingId}/start-photo`
                    : `/meeting-verification/${currentCameraBookingId}/end-photo`;

                const response = await fetch(endpoint, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Show success message
                    showSuccessMessage(data.message);

                    // Close modal
                    closeCameraModal();

                    // Update UI dynamically for start photos
                    if (currentPhotoType === 'start') {
                        updateMeetingCardAfterStartPhoto(currentCameraBookingId, capturedPhotoBlob);
                    } else {
                        // For end photos, still reload to show completion status
                        location.reload();
                    }
                } else {
                    showCameraError(data.message || 'Failed to upload photo. Please try again.');
                }

            } catch (error) {
                showCameraError('Network error. Please check your connection and try again.');
            } finally {
                // Reset button state
                uploadBtn.disabled = false;
                uploadBtnText.classList.remove('hidden');
                uploadBtnLoading.classList.add('hidden');
            }
        }



        // Review-related functions (legacy - now using rateExperienceModal)
        let currentReviewBookingId = null;
        let selectedRating = 0;

        // Legacy star rating functionality removed - now using rateExperienceModal system

        // Helper functions for showing messages
        function showSuccessMessage(message) {
            // Create a temporary success message
            const successDiv = document.createElement('div');
            successDiv.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded';
            successDiv.style.zIndex = '100000';
            successDiv.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span>${message}</span>
                </div>
            `;
            document.body.appendChild(successDiv);

            // Remove after 3 seconds
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.parentNode.removeChild(successDiv);
                }
            }, 3000);
        }

        function showErrorMessage(message) {
            // Create a temporary error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded';
            errorDiv.style.zIndex = '100000';
            errorDiv.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <span>${message}</span>
                </div>
            `;
            document.body.appendChild(errorDiv);

            // Remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
        }

        // Legacy submitReview function removed - now using rateExperienceModal system

        function viewProfile(userId) {
            // Redirect to user's profile page to view reviews
            window.location.href = `/profile/${userId}`;
        }

        // My Reviews Modal functionality
        let currentMyReviewsPage = 1;
        let hasMoreMyReviews = false;
        let isLoadingMyReviews = false;

        function openMyReviewsModal() {
            document.getElementById('myReviewsModal').classList.remove('hidden');
            currentMyReviewsPage = 1;
            hasMoreMyReviews = false;
            loadMyReviews();
        }

        function closeMyReviewsModal() {
            document.getElementById('myReviewsModal').classList.add('hidden');
            // Reset modal state
            document.getElementById('reviewsLoading').classList.remove('hidden');
            document.getElementById('myReviewsContainer').classList.add('hidden');
            document.getElementById('noReviewsMessage').classList.add('hidden');
            document.getElementById('reviewsError').classList.add('hidden');
            document.getElementById('reviewsStats').classList.add('hidden');
            document.getElementById('reviewsPagination').classList.add('hidden');
        }

        async function loadMyReviews() {
            if (isLoadingMyReviews) return;

            isLoadingMyReviews = true;

            // Show loading state
            document.getElementById('reviewsLoading').classList.remove('hidden');
            document.getElementById('myReviewsContainer').classList.add('hidden');
            document.getElementById('noReviewsMessage').classList.add('hidden');
            document.getElementById('reviewsError').classList.add('hidden');
            document.getElementById('reviewsStats').classList.add('hidden');
            document.getElementById('reviewsPagination').classList.add('hidden');

            try {
                const response = await fetch(`/reviews/user/{{ Auth::user()->id }}?page=${currentMyReviewsPage}`);
                const data = await response.json();

                if (data.success) {
                    if (data.reviews.data.length > 0) {
                        displayMyReviews(data.reviews.data, currentMyReviewsPage === 1);
                        displayReviewsStats(data.statistics);

                        // Check if there are more reviews
                        hasMoreMyReviews = data.reviews.current_page < data.reviews.last_page;

                        if (hasMoreMyReviews) {
                            document.getElementById('reviewsPagination').classList.remove('hidden');
                        }

                        document.getElementById('myReviewsContainer').classList.remove('hidden');
                    } else if (currentMyReviewsPage === 1) {
                        // No reviews at all
                        document.getElementById('noReviewsMessage').classList.remove('hidden');
                        // Still show stats if available (edge case)
                        if (data.statistics && data.statistics.total_reviews > 0) {
                            displayReviewsStats(data.statistics);
                        }
                    }

                    document.getElementById('reviewsLoading').classList.add('hidden');
                } else {
                    throw new Error(data.message || 'Failed to load reviews');
                }
            } catch (error) {
                console.error('Error loading reviews:', error);
                document.getElementById('reviewsLoading').classList.add('hidden');
                document.getElementById('reviewsError').classList.remove('hidden');
            } finally {
                isLoadingMyReviews = false;
            }
        }

        function displayMyReviews(reviews, clearContainer = false) {
            const container = document.getElementById('myReviewsContainer');

            if (clearContainer) {
                container.innerHTML = '';
            }

            const reviewsHtml = reviews.map(review => {
                const reviewDate = new Date(review.created_at);
                const formattedDate = reviewDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });

                const bookingDate = review.booking ? new Date(review.booking.booking_date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                }) : 'N/A';

                // Handle profile picture URL with proper fallbacks
                let profilePictureUrl = '{{ asset("images/default-avatar.png") }}';

                // Debug log for this specific review
                if (currentMyReviewsPage === 1 && reviews.indexOf(review) < 3) {

                }

                // For anonymous reviews, always use default avatar
                if (review.is_anonymous) {
                    profilePictureUrl = '{{ asset("images/default-avatar.png") }}';

                } else {
                    // Check multiple ways to get profile picture for non-anonymous reviews
                    if (review.reviewer) {
                        if (review.reviewer.profile_picture_url) {
                            profilePictureUrl = review.reviewer.profile_picture_url;

                        } else if (review.reviewer.profile_picture) {
                            // Manually construct URL from profile_picture field
                            profilePictureUrl = '{{ asset("storage") }}/' + review.reviewer.profile_picture;

                        } else if (review.reviewer.name) {
                            // Use UI Avatars as fallback with user's name
                            const userName = review.reviewer.name;
                            profilePictureUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(userName)}&background=e5e7eb&color=6b7280&size=48`;

                        }
                    } else {

                    }
                }

                const reviewerName = review.is_anonymous ? 'Anonymous User' : (review.reviewer ? review.reviewer.name : 'Unknown User');

                return `
                    <div class="review-card bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 rounded-full border-2 border-gray-200 flex-shrink-0 overflow-hidden bg-gray-100">
                                <img src="${profilePictureUrl}"
                                     alt="${reviewerName}"
                                     class="w-full h-full object-cover"
                                     onerror="handleImageError(this)"

                                     style="background-color: #f3f4f6;">
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <div>
                                        <h5 class="font-semibold text-gray-900">
                                            ${reviewerName}
                                        </h5>
                                        <div class="flex items-center space-x-2 mt-1">
                                            <div class="flex">
                                                ${generateStarRating(review.rating)}
                                            </div>
                                            <span class="text-sm text-gray-600">${review.rating}/5</span>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-xs text-gray-500">${formattedDate}</span>
                                        ${review.booking ? `<div class="text-xs text-gray-400 mt-1">Meeting: ${bookingDate}</div>` : ''}
                                    </div>
                                </div>
                                ${review.review_text ? `
                                    <div class="mt-2 p-3 bg-gray-50 rounded-lg">
                                        <p class="text-gray-700 text-sm">${review.review_text}</p>
                                    </div>
                                ` : ''}
                                ${review.booking ? `
                                    <div class="mt-2 text-xs text-gray-500">
                                        <span class="inline-flex items-center">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Duration: ${review.booking.duration_hours} hour${review.booking.duration_hours > 1 ? 's' : ''}
                                        </span>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML += reviewsHtml;
        }

        function displayReviewsStats(statistics) {
            if (!statistics) return;

            document.getElementById('averageRating').textContent = statistics.average_rating.toFixed(1);
            document.getElementById('totalReviews').textContent = statistics.total_reviews;

            // Generate stars for average rating
            const averageStarsContainer = document.getElementById('averageStars');
            averageStarsContainer.innerHTML = generateStarRating(Math.round(statistics.average_rating));

            document.getElementById('reviewsStats').classList.remove('hidden');
        }

        function generateStarRating(rating) {
            let stars = '';
            for (let i = 1; i <= 5; i++) {
                if (i <= rating) {
                    stars += '<svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
                } else {
                    stars += '<svg class="w-4 h-4 text-gray-300 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
                }
            }
            return stars;
        }

        async function loadMoreMyReviews() {
            if (isLoadingMyReviews || !hasMoreMyReviews) return;

            currentMyReviewsPage++;

            // Show loading state on button
            const loadMoreBtn = document.getElementById('loadMoreReviews');
            const loadMoreText = document.getElementById('loadMoreText');
            const loadMoreLoading = document.getElementById('loadMoreLoading');

            loadMoreText.classList.add('hidden');
            loadMoreLoading.classList.remove('hidden');
            loadMoreBtn.disabled = true;

            try {
                const response = await fetch(`/reviews/user/{{ Auth::user()->id }}?page=${currentMyReviewsPage}`);
                const data = await response.json();

                if (data.success && data.reviews.data.length > 0) {
                    displayMyReviews(data.reviews.data, false);

                    // Check if there are more reviews
                    hasMoreMyReviews = data.reviews.current_page < data.reviews.last_page;

                    if (!hasMoreMyReviews) {
                        document.getElementById('reviewsPagination').classList.add('hidden');
                    }
                } else {
                    hasMoreMyReviews = false;
                    document.getElementById('reviewsPagination').classList.add('hidden');
                }
            } catch (error) {
                console.error('Error loading more reviews:', error);
                showErrorMessage('Failed to load more reviews. Please try again.');
                currentMyReviewsPage--; // Revert page increment
            } finally {
                loadMoreText.classList.remove('hidden');
                loadMoreLoading.classList.add('hidden');
                loadMoreBtn.disabled = false;
            }
        }

        // Close modal when clicking outside
        document.addEventListener('DOMContentLoaded', function() {
            const myReviewsModal = document.getElementById('myReviewsModal');
            if (myReviewsModal) {
                myReviewsModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeMyReviewsModal();
                    }
                });
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !document.getElementById('myReviewsModal').classList.contains('hidden')) {
                closeMyReviewsModal();
            }
        });

        // Handle image loading errors
        function handleImageError(img) {


            // Try different fallback options
            const fallbacks = [
                '{{ asset("images/default-avatar.png") }}',
                '{{ asset("images/default-user.png") }}',
                '{{ asset("assets/images/default-avatar.png") }}',
                'https://ui-avatars.com/api/?name=' + encodeURIComponent(img.alt || 'User') + '&background=e5e7eb&color=6b7280&size=48&rounded=true',
                'https://via.placeholder.com/48x48/e5e7eb/6b7280?text=' + encodeURIComponent((img.alt || 'U').charAt(0).toUpperCase()),
                'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxMiIgeT0iMTIiPgo8cGF0aCBkPSJNMTYgN0E0IDQgMCAxIDEgOCA3QTQgNCAwIDAgMSAxNiA3Wk0xMiAxNEE3IDcgMCAwIDAgNSAyMUgxOUE3IDcgMCAwIDAgMTIgMTRaIiBmaWxsPSIjNkI3MjgwIi8+Cjwvc3ZnPgo8L3N2Zz4K'
            ];

            // Get current fallback index from data attribute, or start at 0
            let currentIndex = parseInt(img.dataset.fallbackIndex || '0');

            if (currentIndex < fallbacks.length - 1) {
                currentIndex++;
                img.dataset.fallbackIndex = currentIndex;
                img.src = fallbacks[currentIndex];

            } else {

                // Remove onerror to prevent infinite loop
                img.onerror = null;
            }
        }

        // Dispute functionality
        function showDisputeModal(bookingId) {
            document.getElementById('disputeBookingId').value = bookingId;
            document.getElementById('disputeModal').classList.remove('hidden');

            // Reset form
            document.getElementById('disputeForm').reset();
            document.getElementById('disputeBookingId').value = bookingId;
            document.getElementById('reasonCharCount').textContent = '0';
            document.getElementById('photoPreviewContainer').classList.add('hidden');
            document.getElementById('photoPreviewContainer').innerHTML = '';
        }

        function closeDisputeModal() {
            document.getElementById('disputeModal').classList.add('hidden');
        }

        function closeDisputeDetailsModal() {
            document.getElementById('disputeDetailsModal').classList.add('hidden');
        }

        // Character count for dispute reason
        document.addEventListener('DOMContentLoaded', function() {
            const reasonTextarea = document.getElementById('disputeReason');
            const charCount = document.getElementById('reasonCharCount');

            if (reasonTextarea && charCount) {
                reasonTextarea.addEventListener('input', function() {
                    charCount.textContent = this.value.length;
                });
            }

            // Photo preview functionality
            const evidencePhotos = document.getElementById('evidencePhotos');
            if (evidencePhotos) {
                evidencePhotos.addEventListener('change', function(e) {
                    const files = Array.from(e.target.files);
                    const container = document.getElementById('photoPreviewContainer');

                    if (files.length > 5) {
                        showErrorMessage('You can only upload up to 5 photos.');
                        e.target.value = '';
                        return;
                    }

                    container.innerHTML = '';

                    if (files.length > 0) {
                        container.classList.remove('hidden');

                        files.forEach((file, index) => {
                            if (file.size > 5 * 1024 * 1024) { // 5MB
                                showErrorMessage(`File ${file.name} is too large. Maximum size is 5MB.`);
                                return;
                            }

                            const reader = new FileReader();
                            reader.onload = function(e) {
                                const div = document.createElement('div');
                                div.className = 'relative';
                                div.innerHTML = `
                                    <img src="${e.target.result}" class="w-full h-20 object-cover rounded border">
                                    <div class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs cursor-pointer" onclick="removePhoto(${index})">×</div>
                                `;
                                container.appendChild(div);
                            };
                            reader.readAsDataURL(file);
                        });
                    } else {
                        container.classList.add('hidden');
                    }
                });
            }

            // Dispute form submission
            const disputeForm = document.getElementById('disputeForm');
            if (disputeForm) {
                disputeForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    submitDispute();
                });
            }
        });

        function removePhoto(index) {
            const input = document.getElementById('evidencePhotos');
            const dt = new DataTransfer();
            const files = Array.from(input.files);

            files.forEach((file, i) => {
                if (i !== index) {
                    dt.items.add(file);
                }
            });

            input.files = dt.files;
            input.dispatchEvent(new Event('change'));
        }

        async function submitDispute() {
            const form = document.getElementById('disputeForm');
            const formData = new FormData(form);

            const submitBtn = document.getElementById('submitDisputeBtn');
            const btnText = document.getElementById('disputeBtnText');
            const btnLoading = document.getElementById('disputeBtnLoading');

            // Show loading state
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
            submitBtn.disabled = true;

            try {
                const response = await fetch('/dispute/raise', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    showSuccessMessage(data.message);
                    closeDisputeModal();
                    // Refresh the page to show updated dispute status
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    if (data.errors) {
                        const errorMessages = Object.values(data.errors).flat().join(', ');
                        showErrorMessage(errorMessages);
                    } else {
                        showErrorMessage(data.message || 'Failed to submit dispute. Please try again.');
                    }
                }
            } catch (error) {
                console.error('Error submitting dispute:', error);
                showErrorMessage('An error occurred while submitting the dispute. Please try again.');
            } finally {
                // Reset loading state
                btnText.classList.remove('hidden');
                btnLoading.classList.add('hidden');
                submitBtn.disabled = false;
            }
        }

        async function viewDisputeDetails(bookingId) {
            try {
                const response = await fetch(`/dispute/details/${bookingId}`, {
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    }
                });

                const data = await response.json();

                if (data.success) {
                    const dispute = data.dispute;
                    const content = document.getElementById('disputeDetailsContent');

                    let evidenceHtml = '';
                    if (dispute.evidence && dispute.evidence.length > 0) {
                        evidenceHtml = `
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-900 mb-2">Evidence Photos:</h4>
                                <div class="grid grid-cols-2 gap-2">
                                    ${dispute.evidence.map(item => `
                                        <img src="/storage/${item.path}" class="w-full h-20 object-cover rounded border cursor-pointer"
                                             onclick="window.open('/storage/${item.path}', '_blank')">
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    }

                    content.innerHTML = `
                        <div class="space-y-4">
                            <div class="bg-orange-50 border border-orange-200 rounded-lg p-3">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-orange-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    <div>
                                        <div class="font-medium text-orange-800">${dispute.dispute_type_display}</div>
                                        <div class="text-sm text-orange-700">${dispute.dispute_status_display}</div>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">Reported Issue:</h4>
                                <p class="text-gray-700 bg-gray-50 p-3 rounded-lg">${dispute.dispute_reason}</p>
                            </div>

                            ${evidenceHtml}

                            <div class="text-sm text-gray-500">
                                <div>Reported on: ${new Date(dispute.disputed_at).toLocaleDateString()}</div>
                                ${dispute.resolved_at ? `<div>Resolved on: ${new Date(dispute.resolved_at).toLocaleDateString()}</div>` : ''}
                            </div>

                            ${dispute.admin_notes ? `
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">Admin Notes:</h4>
                                    <p class="text-gray-700 bg-blue-50 p-3 rounded-lg">${dispute.admin_notes}</p>
                                </div>
                            ` : ''}
                        </div>
                    `;

                    document.getElementById('disputeDetailsModal').classList.remove('hidden');
                } else {
                    showErrorMessage(data.message || 'Failed to load dispute details.');
                }
            } catch (error) {
                showErrorMessage('An error occurred while loading dispute details.');
            }
        }

        // Image Modal Functions
        function openImageModal(imageSrc, title) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalTitle = document.getElementById('modalTitle');

            modalImage.src = imageSrc;
            modalTitle.textContent = title;
            modal.classList.remove('hidden');

            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.add('hidden');

            // Restore body scroll
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside the image
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        closeImageModal();
                    }
                });
            }
        });



        // Post-meeting action functions
        async function rateExperienceModal(bookingId) {
            try {
                // Fetch booking details for the modal
                const response = await fetch(`/booking/${bookingId}/details`, {
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    }
                });

                const data = await response.json();

                if (data.success) {
                    const booking = data.booking;
                    const otherUser = data.other_user;

                    // Populate modal header with booking details
                    document.getElementById('ratingModalUserImage').src = otherUser.profile_picture_url || '/images/default-avatar.png';
                    document.getElementById('ratingModalUserImage').alt = otherUser.name;
                    document.getElementById('ratingModalUserName').textContent = otherUser.name;
                    document.getElementById('ratingModalDateTime').textContent = `${booking.formatted_date} at ${booking.formatted_time}`;
                    document.getElementById('ratingModalDuration').textContent = `${booking.duration_hours} hour${booking.duration_hours > 1 ? 's' : ''}`;
                    document.getElementById('ratingModalBookingId').value = bookingId;

                    // Update placeholder text
                    document.getElementById('modal_review_text').placeholder = `Tell others about your experience with ${otherUser.name}...`;

                    // Reset form
                    resetModalRatingForm();

                    // Create and show modal dynamically
                    let existingModal = document.getElementById('ratingModal');
                    if (existingModal) {
                        existingModal.remove();
                    }

                    const modal = document.createElement('div');
                    modal.id = 'ratingModal';
                    modal.innerHTML = document.querySelector('#ratingModalTemplate').innerHTML;
                    modal.style.cssText = `
                        display: flex !important;
                        position: fixed !important;
                        top: 0 !important;
                        left: 0 !important;
                        width: 100vw !important;
                        height: 100vh !important;
                        background-color: rgba(0, 0, 0, 0.5) !important;
                        z-index: 2147483647 !important;
                        align-items: center !important;
                        justify-content: center !important;
                        padding: 1rem !important;
                    `;
                    document.body.appendChild(modal);

                    // Initialize star colors
                    const stars = modal.querySelectorAll('.modal-star-rating');
                    stars.forEach(star => {
                        star.classList.add('star-unselected');
                        star.style.transform = 'scale(1)';
                    });

                    // Add event listeners for star rating
                    stars.forEach((star, index) => {
                        const rating = index + 1;

                        star.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            setModalRating(rating);
                        });

                        star.addEventListener('mouseover', function() {
                            highlightStars(rating);
                        });

                        star.addEventListener('mouseout', function() {
                            // Add a small delay to prevent conflicts with click events
                            setTimeout(() => {
                                const ratingInput = modal.querySelector('#modalSelectedRating');
                                const currentRating = ratingInput ? parseInt(ratingInput.value) || 0 : 0;
                                console.log('Mouse out - reverting to selected rating:', currentRating);
                                highlightStars(currentRating);
                            }, 50);
                        });
                    });

                    // Reset stars on container mouse leave
                    const starContainer = modal.querySelector('#starRatingContainer');
                    if (starContainer) {
                        starContainer.addEventListener('mouseleave', function() {
                            const ratingInput = modal.querySelector('#modalSelectedRating');
                            const currentRating = ratingInput ? parseInt(ratingInput.value) || 0 : 0;
                            console.log('Container mouse leave - reverting to selected rating:', currentRating);
                            highlightStars(currentRating);
                        });
                    }

                    // Add event listener for checkbox label click
                    const checkboxLabel = modal.querySelector('label[for="modal_is_anonymous"]');
                    const checkbox = modal.querySelector('#modal_is_anonymous');
                    if (checkboxLabel && checkbox) {
                        checkboxLabel.addEventListener('click', function(e) {
                            e.preventDefault();
                            checkbox.checked = !checkbox.checked;
                        });
                    }

                    // Add form submission event listener
                    const form = modal.querySelector('#ratingModalForm');
                    if (form) {
                        console.log('Form found, adding event listener');
                        form.addEventListener('submit', function(e) {
                            console.log('Form submit event triggered');
                            handleModalFormSubmission(e);
                        });
                    } else {
                        console.log('Form not found!');
                    }

                    document.body.style.overflow = 'hidden';
                } else {
                    showErrorMessage('Failed to load booking details.');
                }
            } catch (error) {
                console.error('Error loading booking details:', error);
                showErrorMessage('An error occurred while loading booking details.');
            }
        }

        async function reportNoShow(bookingId) {
            const reason = prompt('Please provide a reason for reporting no-show:');
            if (!reason) return;

            const noShowType = prompt('Who did not show up? Enter:\n1 - Client did not show\n2 - Provider did not show\n3 - Both did not show');
            const typeMap = {
                '1': 'client_no_show',
                '2': 'provider_no_show',
                '3': 'both_no_show'
            };

            if (!typeMap[noShowType]) {
                showErrorMessage('Invalid selection. Please try again.');
                return;
            }

            try {
                const response = await fetch('/post-meeting/report-no-show', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    },
                    body: JSON.stringify({
                        booking_id: bookingId,
                        reason: reason,
                        no_show_type: typeMap[noShowType]
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showSuccessMessage(data.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showErrorMessage(data.message || 'Failed to report no-show.');
                }
            } catch (error) {
                console.error('Error reporting no-show:', error);
                showErrorMessage('An error occurred. Please try again.');
            }
        }

        async function endMeetingManually(bookingId) {
            const reason = prompt('Please provide a reason for ending the meeting manually (optional):');

            if (!confirm('Are you sure you want to end this meeting? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch('/post-meeting/end-meeting', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    },
                    body: JSON.stringify({
                        booking_id: bookingId,
                        reason: reason || ''
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showSuccessMessage(data.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showErrorMessage(data.message || 'Failed to end meeting.');
                }
            } catch (error) {
                console.error('Error ending meeting:', error);
                showErrorMessage('An error occurred. Please try again.');
            }
        }

        // Rating Modal Functions
        let modalSelectedRating = 0;
        const modalRatingTexts = {
            1: 'Poor - Not satisfied',
            2: 'Fair - Below expectations',
            3: 'Good - Met expectations',
            4: 'Very Good - Exceeded expectations',
            5: 'Excellent - Outstanding experience'
        };

        function closeRatingModal() {
            const modal = document.getElementById('ratingModal');
            if (modal) {
                modal.remove();
            }
            document.body.style.overflow = 'auto';
            resetModalRatingForm();
        }

        function resetModalRatingForm() {
            modalSelectedRating = 0;

            // Find elements within the modal
            const modal = document.getElementById('ratingModal');
            if (modal) {
                const ratingInput = modal.querySelector('#modalSelectedRating');
                const reviewText = modal.querySelector('#modal_review_text');
                const anonymousCheckbox = modal.querySelector('#modal_is_anonymous');
                const ratingTextElement = modal.querySelector('#modalRatingText');

                if (ratingInput) ratingInput.value = '';
                if (reviewText) reviewText.value = '';
                if (anonymousCheckbox) anonymousCheckbox.checked = false;
                if (ratingTextElement) ratingTextElement.textContent = 'Click on stars to rate';

                // Reset star colors
                const stars = modal.querySelectorAll('.modal-star-rating');
                stars.forEach(star => {
                    star.classList.remove('star-selected');
                    star.classList.add('star-unselected');
                    star.style.transform = 'scale(1)';
                });
            }
        }

        function setModalRating(rating) {
            modalSelectedRating = rating;

            // Find the rating input within the modal
            const modal = document.getElementById('ratingModal');
            const ratingInput = modal ? modal.querySelector('#modalSelectedRating') : null;

            if (ratingInput) {
                ratingInput.value = rating;
                console.log('Rating set to:', rating, 'Input value:', ratingInput.value);
                console.log('Rating input element:', ratingInput);
            } else {
                console.error('Rating input not found in modal');
            }

            highlightStars(rating);

            // Hide validation error if visible
            const errorDiv = modal ? modal.querySelector('#modalValidationError') : null;
            const dynamicErrorDiv = modal ? modal.querySelector('.dynamic-error-message') : null;

            if (errorDiv) {
                errorDiv.style.display = 'none';
            }
            if (dynamicErrorDiv) {
                dynamicErrorDiv.style.display = 'none';
            }

            // Update rating text
            const ratingTexts = ['', 'Poor', 'Fair', 'Good', 'Very Good', 'Excellent'];
            const ratingTextElement = modal ? modal.querySelector('#modalRatingText') : null;
            if (ratingTextElement) {
                ratingTextElement.textContent = ratingTexts[rating] || 'Click on stars to rate';
            }
        }

        function highlightStars(rating) {
            console.log('highlightStars called with rating:', rating);
            const modal = document.getElementById('ratingModal');
            if (!modal) {
                console.log('Modal not found');
                return;
            }

            const stars = modal.querySelectorAll('.modal-star-rating');
            console.log('Found stars:', stars.length);

            stars.forEach((star, index) => {
                if (index < rating) {
                    // Add selected class and remove unselected class
                    star.classList.remove('star-unselected');
                    star.classList.add('star-selected');
                    star.style.setProperty('transform', 'scale(1.1)', 'important');
                    console.log(`Star ${index + 1}: highlighted (yellow) - classes:`, star.className);
                } else {
                    // Add unselected class and remove selected class
                    star.classList.remove('star-selected');
                    star.classList.add('star-unselected');
                    star.style.setProperty('transform', 'scale(1)', 'important');
                    console.log(`Star ${index + 1}: not highlighted (gray) - classes:`, star.className);
                }
            });
        }

        // Modal form submission handler
        function handleModalFormSubmission(e) {
            // Find the error div within the current modal
            const modal = document.getElementById('ratingModal');
            const errorDiv = modal ? modal.querySelector('#modalValidationError') : null;

            console.log('Modal found:', !!modal);
            console.log('Error div found:', !!errorDiv);
            console.log('Current rating:', modalSelectedRating);

            // Debug: Log form data being submitted
            const formData = new FormData(e.target);
            console.log('Form data being submitted:');
            for (let [key, value] of formData.entries()) {
                console.log(`${key}: "${value}"`);
            }

            // Hide any previous error messages
            if (errorDiv) {
                errorDiv.style.display = 'none';
            }

            if (modalSelectedRating === 0) {
                e.preventDefault();

                // Show error message
                if (errorDiv) {
                    errorDiv.style.display = 'block';
                    errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
                } else {
                    // Create error message dynamically if not found
                    const form = e.target;
                    let dynamicErrorDiv = form.querySelector('.dynamic-error-message');

                    if (!dynamicErrorDiv) {
                        dynamicErrorDiv = document.createElement('div');
                        dynamicErrorDiv.className = 'dynamic-error-message';
                        dynamicErrorDiv.style.cssText = `
                            background-color: #fef2f2;
                            border: 1px solid #fecaca;
                            color: #dc2626;
                            padding: 0.75rem 1rem;
                            border-radius: 0.5rem;
                            margin-bottom: 1rem;
                            display: flex;
                            align-items: center;
                        `;
                        dynamicErrorDiv.innerHTML = `
                            <svg style="width: 1.25rem; height: 1.25rem; margin-right: 0.5rem;" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Please select a rating before submitting.</span>
                        `;

                        // Insert before submit button
                        const submitButtonDiv = form.querySelector('div:last-child');
                        if (submitButtonDiv) {
                            form.insertBefore(dynamicErrorDiv, submitButtonDiv);
                        }
                    }

                    dynamicErrorDiv.style.display = 'flex';
                    dynamicErrorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }

                return false;
            }

            // Show loading state but don't disable the button
            const submitBtnText = modal ? modal.querySelector('#modalSubmitBtnText') : null;
            const submitBtnLoading = modal ? modal.querySelector('#modalSubmitBtnLoading') : null;

            if (submitBtnText) {
                submitBtnText.style.display = 'none';
            }
            if (submitBtnLoading) {
                submitBtnLoading.style.display = 'inline';
            }

            // Allow normal form submission to proceed
            return true;
        }

        // Close modal when clicking outside (delegated event)
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('ratingModal');
            if (modal && e.target === modal) {
                closeRatingModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('ratingModal');
            if (e.key === 'Escape' && modal) {
                closeRatingModal();
            }
        });

        // Debug function to check camera elements
        function debugCameraElements() {
            const elements = ['cameraModal', 'cameraContainer', 'cameraPreview', 'photoCanvas', 'capturedPhoto'];
            console.log('=== Camera Elements Debug ===');
            elements.forEach(id => {
                const element = document.getElementById(id);
                console.log(`${id}: ${element ? '✅ Found' : '❌ Missing'}`);
                if (element) {
                    console.log(`  - Classes: ${element.className}`);
                    console.log(`  - Hidden: ${element.classList.contains('hidden')}`);
                }
            });
            console.log('=== End Debug ===');
        }

        // Initialize camera debug on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Debug camera elements
            setTimeout(debugCameraElements, 1000); // Wait 1 second for all elements to load

            // Also ensure camera elements exist
            setTimeout(ensureCameraElements, 1500);
        });

    </script>

    <!-- Rating Modal Template -->
    <div id="ratingModalTemplate" style="display: none;">
        <style>
            .star-selected svg {
                color: #fbbf24 !important;
                fill: #fbbf24 !important;
            }
            .star-unselected svg {
                color: #d1d5db !important;
                fill: #d1d5db !important;
            }
        </style>
        <div style="background-color: white; border-radius: 1.5rem; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); max-width: 40rem; width: 100%; max-height: 90vh; overflow-y: auto; margin: auto; border: 1px solid rgba(79, 70, 229, 0.1);">
            <!-- Modal Header -->
            <div style="background: linear-gradient(135deg, #4F46E5 0%, #6366F1 50%, #8B5CF6 100%); padding: 2rem; color: white; position: relative; border-radius: 1.5rem 1.5rem 0 0;">
                <button onclick="closeRatingModal()" style="position: absolute; top: 1rem; right: 1rem; color: white; background: rgba(255, 255, 255, 0.2); border-radius: 50%; width: 2.5rem; height: 2.5rem; display: flex; align-items: center; justify-content: center; border: none; cursor: pointer; transition: all 0.2s;" onmouseover="this.style.backgroundColor='rgba(255, 255, 255, 0.3)'" onmouseout="this.style.backgroundColor='rgba(255, 255, 255, 0.2)'">
                    <svg style="width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <img id="ratingModalUserImage"
                         src="/images/default-avatar.png"
                         alt="User"
                         style="width: 4rem; height: 4rem; border-radius: 50%; border: 3px solid rgba(255, 255, 255, 0.3); object-fit: cover;">
                    <div style="flex: 1;">
                        <h2 style="font-size: 1.75rem; font-weight: 700; margin-bottom: 0.25rem; color: white;" id="ratingModalUserName">Loading...</h2>
                        <p style="color: rgba(255, 255, 255, 0.9); font-size: 1rem; margin-bottom: 0.25rem;" id="ratingModalDateTime">Loading...</p>
                        <p style="color: rgba(255, 255, 255, 0.8); font-size: 0.875rem;" id="ratingModalDuration">Loading...</p>
                    </div>
                </div>
            </div>



            <!-- Rating Form -->
            <div style="padding: 2rem;">
                <form id="ratingModalForm" method="POST" action="{{ route('rating.submit') }}" style="display: flex; flex-direction: column; gap: 1.5rem;">
                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="booking_id" id="ratingModalBookingId">

                    <!-- Star Rating -->
                    <div style="text-align: center;">
                        <label style="display: block; font-size: 1.125rem; font-weight: 600; color: #1f2937; margin-bottom: 1rem;">
                            How would you rate your experience?
                        </label>
                        <div style="display: flex; justify-content: center; gap: 0.5rem; margin-bottom: 1rem;" id="starRatingContainer">
                            <button type="button" class="modal-star-rating" data-rating="1" style="background: none; border: none; cursor: pointer; padding: 0.25rem; transition: all 0.2s;">
                                <svg style="width: 2.5rem; height: 2.5rem;" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                            </button>
                            <button type="button" class="modal-star-rating" data-rating="2" style="background: none; border: none; cursor: pointer; padding: 0.25rem; transition: all 0.2s;">
                                <svg style="width: 2.5rem; height: 2.5rem;" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                            </button>
                            <button type="button" class="modal-star-rating" data-rating="3" style="background: none; border: none; cursor: pointer; padding: 0.25rem; transition: all 0.2s;">
                                <svg style="width: 2.5rem; height: 2.5rem;" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                            </button>
                            <button type="button" class="modal-star-rating" data-rating="4" style="background: none; border: none; cursor: pointer; padding: 0.25rem; transition: all 0.2s;">
                                <svg style="width: 2.5rem; height: 2.5rem;" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                            </button>
                            <button type="button" class="modal-star-rating" data-rating="5" style="background: none; border: none; cursor: pointer; padding: 0.25rem; transition: all 0.2s;">
                                <svg style="width: 2.5rem; height: 2.5rem;" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                            </button>
                        </div>
                        <input type="hidden" name="rating" id="modalSelectedRating" required>
                        <p id="modalRatingText" style="font-size: 0.875rem; color: #6b7280; margin-bottom: 1rem;">Click on stars to rate</p>
                    </div>

                    <!-- Review Text -->
                    <div>
                        <label for="modal_review_text" style="display: block; font-size: 0.875rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">
                            Share your experience (optional)
                        </label>
                        <textarea name="review_text"
                                  id="modal_review_text"
                                  rows="4"
                                  style="width: 100%; padding: 0.75rem 1rem; border: 2px solid #e5e7eb; border-radius: 0.75rem; transition: all 0.2s; resize: none; font-family: inherit; font-size: 0.875rem;"
                                  placeholder="Tell others about your experience..."
                                  onfocus="this.style.borderColor='#4F46E5'; this.style.boxShadow='0 0 0 3px rgba(79, 70, 229, 0.1)'"
                                  onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"></textarea>
                        <div style="font-size: 0.75rem; color: #6b7280; margin-top: 0.25rem;">Maximum 1000 characters</div>
                    </div>

                    <!-- Anonymous Option -->
                    <div style="display: flex; align-items: center;">
                        <input type="checkbox"
                               name="is_anonymous"
                               id="modal_is_anonymous"
                               value="1"
                               style="width: 1rem; height: 1rem; color: #4F46E5; background-color: #f9fafb; border: 1px solid #d1d5db; border-radius: 0.25rem;">
                        <label for="modal_is_anonymous" style="margin-left: 0.5rem; font-size: 0.875rem; color: #374151; cursor: pointer; user-select: none;">
                            Submit this review anonymously
                        </label>
                    </div>

                    <!-- Validation Error Message -->
                    <div id="modalValidationError" style="display: none; background-color: #fef2f2; border: 1px solid #fecaca; color: #dc2626; padding: 0.75rem 1rem; border-radius: 0.5rem; margin-bottom: 1rem;">
                        <div style="display: flex; align-items: center;">
                            <svg style="width: 1.25rem; height: 1.25rem; margin-right: 0.5rem;" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                            <span id="modalValidationErrorText">Please select a rating before submitting.</span>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div style="display: flex; justify-content: center; padding-top: 1rem;">
                        <button type="submit"
                                id="modalSubmitBtn"
                                style="padding: 0.75rem 2rem; background: linear-gradient(135deg, #4F46E5 0%, #6366F1 50%, #8B5CF6 100%); color: white; font-weight: 600; border-radius: 0.75rem; border: none; cursor: pointer; transition: all 0.2s; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); transform: translateY(0);"
                                onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 15px -3px rgba(0, 0, 0, 0.1)'"
                                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'">
                            <span id="modalSubmitBtnText">Submit Review</span>
                            <span id="modalSubmitBtnLoading" style="display: none;">
                                <svg style="animation: spin 1s linear infinite; margin-left: -0.25rem; margin-right: 0.5rem; height: 1rem; width: 1rem; color: white; display: inline;" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle style="opacity: 0.25;" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path style="opacity: 0.75;" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Submitting...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <x-toast />
</x-app-layout>
